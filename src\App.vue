<template>
  <div class="app" :class="isLandscape ? 'app--landscape' : 'app--portrait'">
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </div>
</template>

<script setup>
import { useOrientationStore } from "@/stores/orientation";
import { storeToRefs } from "pinia";
import { watch, onMounted, onBeforeMount, computed } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();
const orientationStore = useOrientationStore();
const { orientation, isPortrait, isLandscape } = storeToRefs(orientationStore);

// 判断是否是登录页面
const isLoginPage = computed(() => route.path === '/login');

onBeforeMount(() => {
  // 确保方向状态已初始化
  orientationStore.initOrientation()
})
</script>

<style lang="scss">
.app {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  --van-primary-color: #e74c3c; /* 设置Vant UI的主题色为红色 */

  &--landscape {
    // 横屏特定样式
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  &--portrait {
    // 竖屏特定样式
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .app-header {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1000;
    
    .language-switcher {
      background-color: rgba(255, 255, 255, 0.7);
      border-radius: 4px;
      backdrop-filter: blur(4px);
    }
  }
}

/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 