import { ref, onMounted, onUnmounted } from 'vue'
import { useOrientationStore } from '@/stores/orientation'

/**
 * 屏幕方向管理组合式函数
 * @returns {Object} - 返回屏幕方向状态与方法
 */
export function useOrientation() {
  const orientationStore = useOrientationStore()

  // 直接使用store的计算属性，确保响应性
  const orientation = orientationStore.orientation
  const isLandscape = orientationStore.isLandscape
  const isPortrait = orientationStore.isPortrait
  
  /**
   * CSS强制旋转到横屏
   */
  function forceRotateToLandscape() {
    // 添加CSS类来强制旋转
    const body = document.body;
    body.classList.remove('force-portrait');
    body.classList.add('force-landscape');

    // 更新store状态
    orientationStore.setOrientation('landscape');

    // 触发resize事件，让组件重新计算尺寸
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);
  }

  /**
   * CSS强制旋转到竖屏
   */
  function forceRotateToPortrait() {
    // 添加CSS类来强制旋转
    const body = document.body;
    body.classList.remove('force-landscape');
    body.classList.add('force-portrait');

    // 更新store状态
    orientationStore.setOrientation('portrait');

    // 触发resize事件，让组件重新计算尺寸
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);
  }

  /**
   * 切换到横屏（混合方案）
   */
  function switchToLandscape() {

    try {
      // 首先更新store状态
      orientationStore.setOrientation('landscape')

      // 检查是否支持Screen Orientation API
      if (window.screen && window.screen.orientation && window.screen.orientation.lock) {
        // 检查是否在全屏模式
        const isFullscreen = document.fullscreenElement ||
                            document.webkitFullscreenElement ||
                            document.mozFullScreenElement;

        // 尝试锁定方向
        window.screen.orientation.lock('landscape-primary')
          .then(() => {
            // 成功锁定到landscape-primary
          })
          .catch(err => {
            // 尝试其他横屏方向
            return window.screen.orientation.lock('landscape');
          })
          .then(() => {
            // 成功锁定到landscape
          })
          .catch(err => {
            // 如果硬件锁定失败，使用CSS强制旋转
            forceRotateToLandscape();
          });
      } else {
        // 如果不支持API，直接使用CSS强制旋转
        forceRotateToLandscape();
      }
    } catch (err) {
      // 出错时使用CSS强制旋转作为备选
      forceRotateToLandscape();
    }
  }
  
  /**
   * 切换到竖屏（混合方案）
   */
  function switchToPortrait() {

    try {
      // 首先更新store状态
      orientationStore.setOrientation('portrait')

      // 检查是否支持Screen Orientation API
      if (window.screen && window.screen.orientation && window.screen.orientation.lock) {
        // 检查是否在全屏模式
        const isFullscreen = document.fullscreenElement ||
                            document.webkitFullscreenElement ||
                            document.mozFullScreenElement;

        // 尝试锁定方向
        window.screen.orientation.lock('portrait-primary')
          .then(() => {
            // 成功锁定到portrait-primary
          })
          .catch(err => {
            // 尝试其他竖屏方向
            return window.screen.orientation.lock('portrait');
          })
          .then(() => {
            // 成功锁定到portrait
          })
          .catch(err => {
            // 如果硬件锁定失败，使用CSS强制旋转
            forceRotateToPortrait();
          });
      } else {
        // 如果不支持API，直接使用CSS强制旋转
        forceRotateToPortrait();
      }
    } catch (err) {
      // 出错时使用CSS强制旋转作为备选
      forceRotateToPortrait();
    }
  }
  
  /**
   * 进入全屏模式（提高方向锁定成功率）
   */
  function enterFullscreen() {
    return new Promise((resolve, reject) => {
      const element = document.documentElement;

      if (element.requestFullscreen) {
        element.requestFullscreen().then(resolve).catch(reject);
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
        resolve();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
        resolve();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
        resolve();
      } else {
        reject(new Error('Fullscreen API not supported'));
      }
    });
  }

  /**
   * 退出全屏模式
   */
  function exitFullscreen() {
    return new Promise((resolve, reject) => {
      if (document.exitFullscreen) {
        document.exitFullscreen().then(resolve).catch(reject);
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
        resolve();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
        resolve();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
        resolve();
      } else {
        reject(new Error('Fullscreen API not supported'));
      }
    });
  }

  /**
   * 清除强制旋转
   */
  function clearForceRotation() {
    const body = document.body;
    body.classList.remove('force-landscape', 'force-portrait');

    // 恢复到设备实际方向
    orientationStore.updateOrientationByDevice();

    // 触发resize事件
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);
  }

  /**
   * 全屏模式下切换方向
   */
  function switchOrientationInFullscreen(orientation) {
    return enterFullscreen()
      .then(() => {
        if (orientation === 'landscape') {
          return switchToLandscape();
        } else {
          return switchToPortrait();
        }
      })
      .catch(err => {
        throw err;
      });
  }

  /**
   * 监听屏幕方向变化
   */
  const handleOrientationChange = () => {
    orientationStore.updateOrientationByDevice()
  }
  
  onMounted(() => {
    // 初始化方向
    orientationStore.initOrientation()

    // 添加方向变化监听器
    const mediaQuery = window.matchMedia('(orientation: landscape)')
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleOrientationChange)
    } else if (mediaQuery.addListener) {
      mediaQuery.addListener(handleOrientationChange)
    }
  })
  
  onUnmounted(() => {
    // 移除方向变化监听器
    const mediaQuery = window.matchMedia('(orientation: landscape)')
    if (mediaQuery.removeEventListener) {
      mediaQuery.removeEventListener('change', handleOrientationChange)
    } else if (mediaQuery.removeListener) {
      mediaQuery.removeListener(handleOrientationChange)
    }
  })
  
  return {
    orientation,
    isLandscape,
    isPortrait,
    switchToLandscape,
    switchToPortrait,
    forceRotateToLandscape,
    forceRotateToPortrait,
    clearForceRotation,
    enterFullscreen,
    exitFullscreen,
    switchOrientationInFullscreen
  }
}