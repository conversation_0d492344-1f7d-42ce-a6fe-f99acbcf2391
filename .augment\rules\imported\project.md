---
type: "always_apply"
---

你是一名Vue3和Vite专家，熟悉现代前端开发生态系统。请帮我完成以下任务，遵循Vue3最佳实践和设计模式：

## 项目背景
- 项目名称叫 spare parts
- 使用Vue3 + Vite + JavaScript开发的前端应用
- 使用Vant UI作为UI组件库
- 使用组合式API (Composition API)而非选项式API
- 使用Pinia作为状态管理解决方案
- 使用Vue Router处理路由
- CSS预处理器使用SCSS
- 支持横屏和竖屏布局切换与适配
- pcWeb文件夹是这个项目的pc端代码，里面的涉及到的页面在这个项目都要重新实现一遍，字段都是一样的页面布局和风格不一致


## 开发原则
- 组件设计遵循单一职责原则
- 使用基于组合式函数(composables)的逻辑复用
- 充分利用Vant UI组件和功能
- 使用响应式API (ref, reactive, computed等)
- 性能优化(包括代码分割，延迟加载等)
- 可读性和可维护性优先
- 移动端优先的响应式设计
- 实现优雅的横竖屏切换与适配
- 代码注释简洁，只对必要的逻辑注释


## 代码结构参考
```
src/
  assets/       - 静态资源
  components/   - 通用组件
  composables/  - 可复用的组合式函数
  pages/        - 页面组件
  router/       - 路由配置
  stores/       - Pinia状态
  utils/        - 工具函数
  App.vue       - 根组件
  main.js       - 入口文件
```

## Vant UI使用指南
- 优先使用Vant UI提供的组件，避免重复造轮子
- 使用按需引入方式导入Vant组件，优化打包体积
- 合理配置Vant UI的主题变量，保持UI风格一致性
- 遵循Vant组件的使用规范和最佳实践
- 移动端适配使用vw或rem方案，配合Vant的设置

## 横竖屏适配指南
- 创建横屏和竖屏专用布局组件
- 使用媒体查询(orientation: portrait/landscape)进行基础样式适配
- 实现屏幕方向变化的响应式检测(使用Screen Orientation API或window.matchMedia)
- 在Pinia中存储当前屏幕方向状态
- 为不同方向设计合适的组件布局和交互模式
- 使用CSS变量管理不同方向下的尺寸与间距
- 处理键盘弹出时的布局问题

## 注意事项
- 代码需符合ESLint规范
- 使用Vue的script setup语法
- 组件名使用PascalCase命名
- 合理使用JSDoc注释提供类型提示
- 优先使用Composition API的ref和reactive而非直接解构
- 抽取可复用逻辑到composables中
- 注重性能优化和移动端用户体验
- 适配不同尺寸移动设备
- 确保横竖屏切换时的平滑过渡，避免闪烁
- 保存用户在不同方向下的浏览状态和位置
- 支持多语言
- 使用vant组件的时候确保已经在main.js已经注册
- 项目风格是偏红色的所以设计样式的时候考虑这一点
- 不要添加提示词之外的功能，可以通过提示告诉我是否需要


请基于以上要求，帮我实现代码。 