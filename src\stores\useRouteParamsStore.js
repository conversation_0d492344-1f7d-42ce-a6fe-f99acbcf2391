import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useRouteParamsStore = defineStore(
  'routeParams',
  () => {
    // 存储路由参数的对象
    const params = ref({})

    // 设置参数
    const setParam = (key, value) => {
      params.value[key] = value
    }

    // 获取参数
    const getParam = (key) => {
      return params.value[key]
    }

    // 清除参数
    const clearParam = (key) => {
      if (key) {
        delete params.value[key]
      } else {
        params.value = {}
      }
    }

    return {
      params,
      setParam,
      getParam,
      clearParam
    }
  },
  {
    persist: true
  }
) 