<template>
  <div class="login-page" :class="{ 'landscape': isLandscape }">
    <!-- 品牌区域 -->
    <div class="brand-area">
      <AppLogo :title="$t('common.appName')" :slogan="$t('common.slogan')" />
    </div>
    
    <!-- 登录表单 -->
    <div class="login-form-wrapper">
<!--      <h2 class="form-title">{{ $t('login.title') }}</h2>-->
      
      <van-form @submit="onSubmit" class="login-form">
        <van-field
          v-model="userName"
          name="userName"
          :placeholder="$t('login.username')"
          :rules="[{ required: true, message: $t('login.usernameRequired') }]"
          autocomplete="username"
        >
          <template #left-icon>
            <van-icon name="user-o" />
          </template>
        </van-field>
        
        <van-field
          v-model="password"
          type="password"
          name="password"
          :placeholder="$t('login.password')"
          :rules="[{ required: true, message: $t('login.passwordRequired') }]"
          autocomplete="current-password"
        >
          <template #left-icon>
            <van-icon name="lock" />
          </template>
        </van-field>
        
        <!-- <div class="field-options">
          <van-checkbox v-model="rememberMe" shape="square" icon-size="16px" checked-color="#ffffff">
            <span class="remember-text">{{ $t('login.rememberMe') }}</span>
          </van-checkbox>
        </div> -->
        
        <van-button 
          block 
          class="login-button"
          :loading="isLoading"
          native-type="submit"
        >
          {{ $t('login.loginButton') }}
        </van-button>
      </van-form>
      
      <!-- 底部信息 -->
      <div class="footer-info">
        <p class="copyright">{{ $t('common.copyright') }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onBeforeMount } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useOrientationStore } from '@/stores/orientation'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import AppLogo from '@/components/AppLogo.vue'
import apiService from '@/utils/api'
import { useI18n } from 'vue-i18n'

// 获取i18n实例
const { t } = useI18n()

// 获取屏幕方向
const orientationStore = useOrientationStore()
const { isLandscape } = storeToRefs(orientationStore)

// 获取用户store
const userStore = useUserStore()

// 表单数据
const userName = ref('')
const password = ref('')
const isLoading = ref(false)

const router = useRouter()

// 登录前检查是否已登录
onBeforeMount(() => {
  // 如果已登录则直接跳转到首页
  if (userStore.isLoggedIn()) {
    router.replace('/home')
  }
})

// 登录提交
const onSubmit = async () => {
  if (!(password.value && userName.value)) {
    showToast({
      message: t('login.usernameRequired'),
      position: 'top'
    })
    return
  }

  try {
    isLoading.value = true
    
    const res = await apiService.user.login({
      password: password.value,
      mobile: userName.value,
    })
    
    if (res) {
      // 将用户信息存储到store中
      userStore.setUserInfo(res)
      
      
      // 跳转到首页
      router.push('/home')
    } else {
      showToast({
        message: t('login.loginFail'),
        position: 'top'
      })
    }
  } catch (error) {
    showToast({
      message: t('login.loginError'),
      position: 'top'
    })
  } finally {
    isLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  /*background: linear-gradient(135deg, #e74c3c, #f39c12);*/
  padding: 30px 20px 20px;
  
  // 横屏模式布局
  &.landscape {
    padding: 10px 40px;
    flex-direction: column;
    justify-content: flex-start;
    
    .brand-area {
      padding-bottom: 10px;
    }
    
    .login-form-wrapper {
      width: 90%;
      max-width: 450px;
      margin: 0 auto;
    }
  }
}

// 品牌区域
.brand-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 0 20px;
  color: white;
  
  :deep(.app-logo) {
    .logo-container {
      width: 100px;
      height: 100px;
      
      .logo-image {
        filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
      }
    }
    
    .logo-text {
      .app-name {
        font-size: 28px;
        font-weight: 600;
      }
      
      .app-slogan {
        font-size: 16px;
        opacity: 0.9;
      }
    }
  }
}

// 登录表单
.login-form-wrapper {
  padding: 0;
  margin: 0 auto;
  width: 100%;
  max-width: 350px;
  
  .form-title {
    font-size: 22px;
    color: #fff;
    text-align: center;
    margin: 0 0 20px;
    font-weight: 500;
    letter-spacing: 1px;
  }
  
  .login-form {
    margin-bottom: 20px;
    
    // 覆盖Vant默认的表单样式
    :deep(.van-field) {
      margin-bottom: 16px;
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.85);
      backdrop-filter: blur(4px);
      
      &:after {
        display: none;
      }
      
      .van-field__left-icon {
        margin-right: 10px;
        color: #e74c3c;
      }
      
      .van-field__control {
        color: #333;
        
        &::placeholder {
          color: #999;
        }
      }
      
      &.van-field--error {
        .van-field__control {
          color: #f44;
        }
      }
    }
  }
  
  .field-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    color: #fff;
    
    .remember-text {
      color: #fff;
    }
    
    .forgot-link {
      color: #fff;
      font-size: 14px;
      opacity: 0.9;
      
      &:active {
        opacity: 0.7;
      }
    }
  }
  
  .login-button {
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    background: rgba(19, 80, 211, 0.85);
    border: none;
    backdrop-filter: blur(4px);
    color: white;
    margin-top: 5px;
    
    &:active {
      background: rgba(255, 255, 255, 0.35);
    }
    
    :deep(.van-button__text) {
      color: white;
    }
    
    :deep(.van-loading__spinner) {
      color: white;
    }
  }
  
  .footer-info {
    margin-top: 30px;
    text-align: center;
    
    .copyright {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
      margin: 0;
    }
  }
}
</style> 