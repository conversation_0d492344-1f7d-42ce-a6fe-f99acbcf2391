<template>
  <div class="warranty-list">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished || list.length === 0"
        :finished-text="$t('common.noMoreData')"
        @load="onLoad"
        :immediate-check="true"
      >
        <!-- 无数据状态 -->
        <div v-if="list.length === 0" class="empty-state">
          <van-empty :description="$t('warranty.noData')" />
        </div>

        <!-- 质保卡片列表 -->
        <div class="warranty-cards">
          <div
            v-for="item in list"
            :key="item.id"
            class="warranty-card"
            :class="{
              'pending-card': item.status === '1',
              'confirmed-card': item.status === '2',
            }"
          >
            <!-- 卡片头部:状态+序列号 -->
            <div class="card-header">
              <div class="status-badge">
                <span class="status-dot"></span>
                <span class="status-text">
                  {{
                    item.status === "1"
                      ? $t("warranty.pendingStatus")
                      : $t("warranty.confirmedStatus")
                  }}
                </span>
              </div>
              <div class="serial-number">
                <span class="label">SN:</span>
                <span class="value">{{ item.serialNumber }}</span>
              </div>
            </div>
            
            <!-- 卡片内容 -->
            <div class="card-content">
              <!-- 设备信息行 -->
              <div class="info-row product-row">
                <div class="label">{{ $t("warranty.equipmentInfo") }}</div>
                <div class="value">
                  <span class="brand">{{ item.brand }}</span>
                  <span class="model">{{ item.model }}</span>
                  <span class="type">{{ item.productType }}</span>
                  <span v-if="item.bom" class="service-code">{{ item.bom }}</span>
                </div>
              </div>

              <!-- 客户信息行 -->
              <div class="info-row customer-row">
                <div class="label">{{ $t("warranty.customerInfo") }}</div>
                <div class="value">
                  <div class="customer-info">
                    <div class="customer-name">
                      <van-icon name="contact" />
                      <span>{{ item.linkman }}</span>
                    </div>
                    <div class="customer-contact">
                      <span v-if="item.phone">
                        <van-icon name="phone-o" />
                        <span>{{ item.phone }}</span>
                      </span>
                      <span v-if="item.city">
                        <van-icon name="location-o" />
                        <span>{{ item.city }}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 日期信息行 -->
              <div class="info-row date-row">
                <div class="value">
                  <div class="date-grid">
                    <div class="date-item">
                      <div class="date-label">{{ $t("warranty.salesDate") }}</div>
                      <div class="date-value">{{ item.salesDate }}</div>
                    </div>
                    <div class="date-item">
                      <div class="date-label">{{ $t("warranty.createDate") }}</div>
                      <div class="date-value">{{ item.updateDate }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 索赔记录列表 - 移动到操作按钮上方 -->
            <div class="claims-list" v-if="item.warrantyDates && item.warrantyDates.length > 0">
              <div class="claims-header">
                <van-icon name="records" />
                <span>{{ $t("warranty.claimRecords") }} ({{ item.warrantyDates.length }})</span>
              </div>
              <div class="claim-items">
                <div 
                  class="claim-item" 
                  v-for="(date, index) in item.warrantyDates" 
                  :key="index"
                  @click="onViewClaim(item, date)"
                >
                  <div class="claim-info">
                    <div class="claim-date">
                      <van-icon name="clock-o" />
                      <span>{{ date.updateDate }}</span>
                    </div>
                  </div>
                  <div class="claim-action" v-if="isWithin24Hours(date.createDate)">
                    <van-icon 
                      name="delete" 
                      class="delete-icon" 
                      @click.stop="onInvalidClaim(date)" 
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 操作按钮区域 -->
            <div class="card-actions">
              <div class="action-group">
                <van-button
                  class="action-btn"
                  size="small"
                  icon="description"
                  @click="onViewDetail(item)"
                >
                  {{ $t("warranty.details") }}
                </van-button>
                
                <van-button
                  class="action-btn"
                  size="small"
                  icon="plus"
                  @click="onAddClaim(item)"
                >
                  {{ $t("warranty.addClaimRecord") }}
                </van-button>
                
                <template v-if="item.status === '1'">
                  <van-button
                    class="action-btn confirm-btn"
                    size="small"
                    icon="success"
                    @click="onConfirm(item)"
                  >
                    {{ $t("warranty.confirm") }}
                  </van-button>
                  
                  <van-button
                    class="action-btn invalid-btn"
                    size="small"
                    icon="cross"
                    @click="onInvalid(item)"
                  >
                    {{ $t("warranty.invalid") }}
                  </van-button>
                </template>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>

  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, toRefs, watch } from "vue";

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  finished: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "refresh",
  "load",
  "view-detail",
  "confirm",
  "invalid",
  "view-claim",
  "invalid-claim",
  "add-claim"
]);

// 解构并创建本地状态的引用
const { loading, finished } = toRefs(props);
const refreshing = ref(false);

// 监听loading状态变化，当loading状态变为false时，确保refreshing也设置为false
watch(loading, (newVal) => {
  if (newVal === false && refreshing.value === true) {
    refreshing.value = false;
  }
});

// 事件处理方法
const onRefresh = () => {
  refreshing.value = true;
  emit("refresh");
};

const onLoad = () => {
  emit("load");
};

const onViewDetail = (item) => {
  emit("view-detail", item);
};

const onConfirm = (item) => {
  emit("confirm", item);
};

const onInvalid = (item) => {
  emit("invalid", item);
};

const onViewClaim = (item, date) => {
  emit("view-claim", item, date);
};

const onInvalidClaim = (date) => {
  emit("invalid-claim", date);
};

const onAddClaim = (item) => {
  emit("add-claim", item);
};

// 辅助函数
const isWithin24Hours = (dateString) => {
  if (!dateString) return false;
  const currentDate = new Date();
  const givenDate = new Date(dateString);
  const timeDifference = Math.abs(currentDate.getTime() - givenDate.getTime());
  return timeDifference <= 24 * 60 * 60 * 1000;
};
</script>

<style lang="scss" scoped>
.warranty-list {
  position: relative;
  min-height: 80vh;
  background-color: #f7f8fa;
  padding: 6px 8px;

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30vh;
    margin-top: 20px;
  }

  .warranty-cards {
    .warranty-card {
      position: relative;
      margin-bottom: 12px;
      background-color: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

      &.pending-card {
        .status-badge {
          background-color: #fff7e8;
          border-color: #ffedd2;
          
          .status-dot {
            background-color: #ff976a;
          }
          
          .status-text {
            color: #ff976a;
          }
        }
      }

      &.confirmed-card {
        .status-badge {
          background-color: #e8fff0;
          border-color: #d0f1dd;
          
          .status-dot {
            background-color: #07c160;
          }
          
          .status-text {
            color: #07c160;
          }
        }
      }

      // 卡片头部
      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 12px;
        border-bottom: 1px solid #f5f5f5;
        
        .status-badge {
          display: inline-flex;
          align-items: center;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          border: 1px solid transparent;
          
          .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 4px;
          }
          
          .status-text {
            font-weight: 500;
          }
        }
        
        .serial-number {
          font-size: 14px;
          display: flex;
          align-items: center;
          
          .label {
            color: #969799;
            margin-right: 4px;
          }
          
          .value {
            color: #323233;
            font-weight: 600;
            letter-spacing: 0.5px;
          }
        }
      }
      
      // 卡片内容
      .card-content {
        padding: 10px 12px;
        
        .info-row {
          display: flex;
          flex-direction: column;
          margin-bottom: 12px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .label {
            font-size: 13px;
            color: #969799;
            margin-bottom: 4px;
          }
          
          .value {
            font-size: 14px;
            color: #323233;
          }
        }
        
        // 产品信息行
        .product-row {
          .value {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            
            .brand {
              font-weight: 600;
              color: #2c3e50;
              margin-right: 4px;
            }
            
            .model, .type {
              display: inline-block;
              background-color: #f7f8fa;
              padding: 2px 6px;
              border-radius: 4px;
              font-size: 13px;
            }
            
            .service-code {
              font-size: 12px;
              background-color: #f2f3f5;
              color: #909399;
              padding: 1px 6px;
              border-radius: 4px;
            }
          }
        }
        
        // 客户信息行
        .customer-row {
          .customer-info {
            .customer-name {
              display: flex;
              align-items: center;
              margin-bottom: 4px;
              font-weight: 500;
              
              .van-icon {
                margin-right: 6px;
                color: #576b95;
              }
            }
            
            .customer-contact {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              font-size: 13px;
              color: #646566;
              
              span {
                display: flex;
                align-items: center;
                
                .van-icon {
                  margin-right: 4px;
                  color: #969799;
                }
              }
            }
          }
        }
        
        // 日期信息行
        .date-row {
          .date-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            
            .date-item {
              .date-label {
                font-size: 12px;
                color: #969799;
                margin-bottom: 2px;
              }
              
              .date-value {
                font-size: 13px;
                color: #323233;
                font-family: monospace;
              }
            }
          }
        }
      }
      
      // 卡片操作区
      .card-actions {
        padding: 10px 12px;
        background-color: #f7f8fa;
        border-top: 1px solid #f5f5f5;
        
        .action-group {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .action-btn {
            border-radius: 15px;
            font-size: 12px;
            height: 28px;
            padding: 0 10px;
            
            &.confirm-btn {
              color: #07c160;
              background: #e8fff0;
              border-color: #07c160;
            }
            
            &.invalid-btn {
              color: #ee0a24;
              background: #fff2f2;
              border-color: #ee0a24;
            }
          }
        }
      }
      
      // 索赔记录列表
      .claims-list {
        background-color: #fff;
        border-top: 1px solid #ebedf0;
        padding: 4px 12px;
        
        .claims-header {
          display: flex;
          align-items: center;
          padding: 6px 0;
          font-size: 13px;
          font-weight: 500;
          color: #ee0a24;
          
          .van-icon {
            margin-right: 6px;
            font-size: 16px;
          }
        }
        
        .claim-items {
          .claim-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 8px;
            border-bottom: 1px solid #f5f5f5;
            background-color: #fafafa;
            border-radius: 6px;
            margin-bottom: 4px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            &:active {
              background-color: #f0f0f0;
            }
            
            .claim-info {
              flex: 1;
              
              .claim-date {
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #576b95;
                
                .van-icon {
                  margin-right: 4px;
                  color: #969799;
                }
              }
            }
            
            .claim-action {
              padding-left: 8px;
              .delete-icon {
                font-size: 16px;
                color: #ee0a24;
                padding: 4px;
              }
            }
          }
        }
      }
    }
  }

  .float-action {
    position: fixed;
    right: 16px;
    bottom: 24px;
    width: 48px;
    height: 48px;
    border-radius: 24px;
    background-color: #ee0a24;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(238, 10, 36, 0.3);
    z-index: 999;

    .van-icon {
      font-size: 24px;
    }
  }
}
</style> 