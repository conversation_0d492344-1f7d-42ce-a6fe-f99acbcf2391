/**
 * 移动端文件下载工具
 * 解决手机端 window.open 下载文件的兼容性问题
 */

/**
 * 检测是否为iOS Safari
 */
const isIOSSafari = () => {
  const userAgent = navigator.userAgent
  return /iPad|iPhone|iPod/.test(userAgent) && /^((?!chrome|android).)*safari/i.test(userAgent)
}

/**
 * 移动端文件下载函数（强制下载，不预览）
 * @param {string} url - 文件下载URL
 * @param {string} filename - 可选的文件名
 * @param {Function} onError - 错误回调函数
 * @returns {boolean} - 是否成功触发下载
 */
export const downloadFileForMobile = (url, filename = '', onError = null) => {
  try {
    // 方法1: 使用 a 标签强制下载
    const link = document.createElement('a')
    link.href = url
    link.download = filename || 'download'
    link.target = '_blank'
    link.rel = 'noopener noreferrer'
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // iOS Safari 备选方案
    if (isIOSSafari()) {
      setTimeout(() => {
        const newWindow = window.open(url, '_blank')
        if (!newWindow && onError) {
          onError(new Error('Download blocked'))
        }
      }, 100)
    }

    return true

  } catch (error) {
    console.warn('Download failed:', error)

    // 降级方案：直接使用 window.open
    try {
      window.open(url, '_blank')
      return true
    } catch (fallbackError) {
      if (onError) onError(fallbackError)
      return false
    }
  }
}





/**
 * 移动端文件预览函数（在新窗口打开，不强制下载）
 * @param {string} url - 文件预览URL
 * @param {Function} onError - 错误回调函数
 * @returns {boolean} - 是否成功打开预览
 */
export const previewFileForMobile = (url, onError = null) => {
  try {
    const newWindow = window.open(url, '_blank', 'noopener,noreferrer')

    if (!newWindow) {
      if (onError) {
        onError(new Error('Preview blocked by popup blocker'))
      }
      return false
    }

    return true
  } catch (error) {
    console.warn('Preview failed:', error)
    if (onError) {
      onError(error)
    }
    return false
  }
}

/**
 * 移动端Blob文件预览函数
 * @param {Blob} blob - 文件Blob对象
 * @param {Function} onError - 错误回调函数
 * @returns {boolean} - 是否成功打开预览
 */
export const previewBlobForMobile = (blob, onError = null) => {
  try {
    const url = URL.createObjectURL(blob)
    const newWindow = window.open(url, '_blank', 'noopener,noreferrer')

    if (!newWindow) {
      URL.revokeObjectURL(url)
      if (onError) {
        onError(new Error('Preview blocked by popup blocker'))
      }
      return false
    }

    // 延迟释放URL，确保文件能正常加载
    setTimeout(() => {
      URL.revokeObjectURL(url)
    }, 5000)

    return true
  } catch (error) {
    console.warn('Blob preview failed:', error)
    if (onError) {
      onError(error)
    }
    return false
  }
}

// 默认导出
export default downloadFileForMobile
