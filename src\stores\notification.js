import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const unreadCount = ref(0)
  
  // 动作
  function setUnreadCount(count) {
    unreadCount.value = count
  }
  
  function incrementUnreadCount() {
    unreadCount.value++
  }
  
  function decrementUnreadCount() {
    if (unreadCount.value > 0) {
      unreadCount.value--
    }
  }
  
  function clearUnreadCount() {
    unreadCount.value = 0
  }
  
  return {
    unreadCount,
    setUnreadCount,
    incrementUnreadCount,
    decrementUnreadCount,
    clearUnreadCount
  }
}, {
  persist: true
}) 