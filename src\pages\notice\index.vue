<template>
  <div class="page">
    <!-- 标准导航栏 -->
    <van-nav-bar
      :title="$t('notice.title')"
      left-arrow
      @click-left="$router.back()"
      fixed
    >
      <template #right>
        <van-badge v-if="unreadCount > 0" :content="unreadCount" max="99">
          <van-icon name="bell" size="18" />
        </van-badge>
      </template>
    </van-nav-bar>

    <!-- 搜索栏 -->
    <div class="search-container">
      <van-search
        v-model="searchParams.materielCode"
        :placeholder="$t('notice.partNumber')"
        @search="onSearch"
        @clear="onClear"
        show-action
      >
        <template #action>
          <div @click="onSearch">{{ $t('notice.search') }}</div>
        </template>
      </van-search>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-container">
      <van-tabs v-model:active="activeStatusTab" @change="onStatusTabChange" class="status-tabs">
        <van-tab :title="$t('notice.status.all')" name="" />
        <van-tab name="0">
          <template #title>
            <span>{{ $t('notice.status.unread') }}</span>
            <van-badge v-if="unreadCount > 0" :content="unreadCount" />
          </template>
        </van-tab>
        <van-tab :title="$t('notice.status.read')" name="1" />
      </van-tabs>

      <div class="equipment-filter-wrapper">
        <van-button
          type="default"
          size="small"
          icon="filter-o"
          @click="showEquipmentPicker = true"
          class="equipment-filter-btn"
          :class="{ 'has-filter': hasEquipmentFilter }"
        >
          {{ equipmentFilterTitle }}
        </van-button>

        <!-- 清空按钮 -->
        <van-button
          v-if="hasEquipmentFilter"
          type="default"
          size="small"
          icon="clear"
          @click="clearEquipmentFilter"
          class="clear-filter-btn"
          :title="$t('notice.filter.clear')"
        />
      </div>
    </div>

    <!-- 公告列表 -->
    <div class="content-container">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          v-model:error="error"
          :finished="finished"
          :finished-text="$t('common.noMoreData')"
          :error-text="$t('notice.loadError')"
          :loading-text="$t('common.loading')"
          :immediate-check="false"
          :offset="100"
          @load="loadNotices"
        >
          <!-- 公告项 -->
          <div v-for="(item, index) in noticeList" :key="item.recordId || index" class="notice-item">
            <div class="notice-header">
              <div class="status-info">
                <van-tag
                  :type="item.readFlag === '1' ? 'success' : 'danger'"
                  size="small"
                  class="status-tag"
                >
                  {{ item.readFlag === '1' ? $t('notice.status.read') : $t('notice.status.unread') }}
                </van-tag>
                <span class="notice-time">{{ formatDate(item.createDate) }}</span>
              </div>
            </div>

            <div class="notice-body">
              <!-- 公告描述 -->
              <div v-if="item.description" class="notice-description">
                {{ item.description }}
              </div>

              <!-- PDF文件 -->
              <div v-if="item.files" class="pdf-file" @click="viewPdf(item)">
                <van-icon name="description" class="pdf-icon" />
                <span class="pdf-name">{{ getPdfFileName(item.files) }}</span>
                <van-icon name="arrow" class="arrow-icon" />
              </div>

              <!-- 设备信息 -->
              <div class="equipment-info" v-if="hasEquipmentInfo(item)">
                <div class="info-row" v-if="item.model">
                  <span class="info-label">{{ $t('notice.list.equipmentModel') }}:</span>
                  <span class="info-value">{{ item.model }}</span>
                </div>
                <div class="info-row" v-if="item.bom">
                  <span class="info-label">{{ $t('notice.list.serviceCode') }}:</span>
                  <span class="info-value">{{ item.bom }}</span>
                </div>
                <div class="info-row" v-if="item.materielCode">
                  <span class="info-label">{{ $t('notice.list.partNumber') }}:</span>
                  <span class="info-value">{{ item.materielCode }}</span>
                </div>
                <div class="info-row" v-if="item.serialNumberRange">
                  <span class="info-label">{{ $t('notice.list.serialNumberRange') }}:</span>
                  <span class="info-value">{{ item.serialNumberRange }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <van-empty
            v-if="!loading && noticeList.length === 0"
            :description="$t('notice.empty')"
            image="search"
          />
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 设备筛选弹窗 -->
    <van-popup
      v-model:show="showEquipmentPicker"
      position="bottom"
      round
      safe-area-inset-bottom
    >
      <van-cascader
        v-model="selectedEquipment"
        :title="$t('notice.filter.equipment')"
        :options="equipmentOptions"
        :field-names="{ text: 'fileName', value: 'id', children: 'children' }"
        @close="showEquipmentPicker = false"
        @finish="onEquipmentSelect"
      />
    </van-popup>
  </div>
</template>

<!--
  移动端公告页面

  功能特性：
  - 公告列表展示（支持分页和无限滚动）
  - 搜索功能（按零件号搜索）
  - 状态筛选（全部/已读/未读）
  - 设备筛选（四级级联选择）
  - PDF文件查看
  - 下拉刷新和上拉加载
  - 响应式设计和触摸友好交互
  - 国际化支持


  技术栈：
  - Vue 3 Composition API
  - Vant 4 移动端组件库
  - Vue I18n 国际化
  - Pinia 状态管理

  作者：AI Assistant
  创建时间：2025-01-10
-->

<script setup>
import { ref, onMounted, onUnmounted, computed, inject } from 'vue'
import { useI18n } from 'vue-i18n'
import { showToast, Dialog } from 'vant'
import apiService from '@/utils/api'
import { BASE_FILE_URL } from '@/config/config'
import { previewFileForMobile } from '@/utils/mobileDownload'

const { t } = useI18n()

// 注入全局变量
const notice_noRead = inject('notice_noRead', ref(0))

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const error = ref(false)
const noticeList = ref([])
const showEquipmentPicker = ref(false)
const selectedEquipment = ref([])
const equipmentOptions = ref([])
const activeStatusTab = ref('')

// 搜索参数
const searchParams = ref({
  id: '',
  materielCode: '',
  readFlag: '',
  brand: '',
  productType: '',
  model: '',
  bom: '',
  startDate: null,
  endDate: null,
  total: 0,
  pageNo: 1,
  pageSize: 10
})

// 计算属性
const equipmentFilterTitle = computed(() => {
  if (searchParams.value.brand || searchParams.value.productType ||
      searchParams.value.model || searchParams.value.bom) {
    return `${searchParams.value.brand || ''} ${searchParams.value.productType || ''} ${searchParams.value.model || ''} ${searchParams.value.bom || ''}`.trim()
  }
  return t('notice.filter.equipment')
})

// 是否有设备筛选条件
const hasEquipmentFilter = computed(() => {
  return !!(searchParams.value.brand || searchParams.value.productType ||
           searchParams.value.model || searchParams.value.bom)
})



// 方法
const loadNotices = async (isRefresh = false) => {
  if (loading.value && !isRefresh) return

  try {
    loading.value = true
    error.value = false

    if (isRefresh) {
      searchParams.value.pageNo = 1
      finished.value = false
    }

    // 使用真实API获取数据
    const result = await apiService.notification.getMyNotice(searchParams.value)

    if (isRefresh || searchParams.value.pageNo === 1) {
      noticeList.value = result.data || []
    } else {
      noticeList.value = [...noticeList.value, ...(result.data || [])]
    }

    searchParams.value.pageNo = result.pageNo || searchParams.value.pageNo
    searchParams.value.total = result.total || 0

    // 判断是否加载完成
    if (noticeList.value.length >= searchParams.value.total ||
        (result.data && result.data.length < searchParams.value.pageSize)) {
      finished.value = true
    }

    // 更新未读通知数量
    updateUnreadCount()

  } catch (err) {
    error.value = true

    // 根据错误类型显示不同的提示
    if (err.code === 'NETWORK_ERROR') {
      showToast('网络连接失败，请检查网络')
    } else if (err.code === 'TIMEOUT') {
      showToast('请求超时，请重试')
    } else {
      showToast(t('notice.loadError'))
    }

    // 如果是首次加载失败，保持当前数据
    if (searchParams.value.pageNo > 1) {
      searchParams.value.pageNo -= 1
    }
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const onRefresh = () => {
  refreshing.value = true
  loadNotices(true)
}

// 防抖搜索
let searchTimeout = null
const onSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    searchParams.value.pageNo = 1
    finished.value = false
    loadNotices(true)
  }, 300)
}

const onClear = () => {
  searchParams.value.materielCode = ''
  onSearch()
}

// 清空设备筛选
const clearEquipmentFilter = () => {
    // 清空设备筛选参数
    searchParams.value.brand = ''
    searchParams.value.productType = ''
    searchParams.value.model = ''
    searchParams.value.bom = ''
    searchParams.value.id = ''

    // 重置级联选择器
    selectedEquipment.value = []

    // 重新搜索
    onSearch()
}

// 状态标签切换
const onStatusTabChange = (name) => {
  searchParams.value.readFlag = name
  searchParams.value.pageNo = 1
  finished.value = false
  loadNotices(true)
}



// 检查是否有设备信息
const hasEquipmentInfo = (item) => {
  return item.model || item.bom || item.materielCode || item.serialNumberRange
}

const loadEquipmentMenu = async () => {
  try {
    // 使用真实API获取设备菜单
    const result = await apiService.system.getMenu()
    equipmentOptions.value = result || []
  } catch (err) {
    showToast('获取设备菜单失败')
  }
}

const onEquipmentSelect = ({ selectedOptions }) => {
  showEquipmentPicker.value = false

  if (!selectedOptions || !Array.isArray(selectedOptions)) {
    return
  }

  // 重置所有设备相关参数
  searchParams.value.brand = ''
  searchParams.value.productType = ''
  searchParams.value.model = ''
  searchParams.value.bom = ''
  searchParams.value.id = ''

  // 根据选择的层级设置参数
  try {
    if (selectedOptions.length > 0 && selectedOptions[0]?.fileName) {
      searchParams.value.brand = selectedOptions[0].fileName
      searchParams.value.id = selectedOptions[0].id
    }
    if (selectedOptions.length > 1 && selectedOptions[1]?.fileName) {
      searchParams.value.productType = selectedOptions[1].fileName
      searchParams.value.id = selectedOptions[1].id
    }
    if (selectedOptions.length > 2 && selectedOptions[2]?.fileName) {
      searchParams.value.model = selectedOptions[2].fileName
      searchParams.value.id = selectedOptions[2].id
    }
    if (selectedOptions.length > 3 && selectedOptions[3]?.fileName) {
      searchParams.value.bom = selectedOptions[3].fileName
      searchParams.value.id = selectedOptions[3].id
    }

    onSearch()
  } catch (error) {
    showToast('设备选择失败')
  }
}

const viewPdf = async (item) => {
  if (!item?.files || !item?.recordId) {
    showToast('PDF文件不存在')
    return
  }

  try {
    // 标记为已读
    await apiService.notification.readNotice({ recordId: item.recordId })

    // 更新本地状态
    item.readFlag = '1'

    // 打开PDF
    const fullUrl = `${window.location.origin}${BASE_FILE_URL}${item.files}`
    // 使用移动端优化的预览函数
    const success = previewFileForMobile(fullUrl, (error) => {
      showToast('请允许弹出窗口以查看PDF')
    })

    if (!success) {
      showToast('打开PDF失败')
      return
    }

    // 更新未读数量
    updateUnreadCount()

  } catch (err) {
    showToast('打开PDF失败')
  }
}

const updateUnreadCount = async () => {
  try {
    // 使用真实API获取未读数量
    const result = await apiService.notification.getUnreadCount()
    if (notice_noRead && result) {
      notice_noRead.value = result.length || 0
    }
  } catch (err) {
    // 静默处理错误
  }
}

// 缓存格式化结果以提升性能
const dateFormatCache = new Map()
const formatDate = (dateStr) => {
  if (!dateStr) return ''

  if (dateFormatCache.has(dateStr)) {
    return dateFormatCache.get(dateStr)
  }

  try {
    const date = new Date(dateStr)
    const formatted = date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
    dateFormatCache.set(dateStr, formatted)
    return formatted
  } catch (error) {
    return dateStr // 如果格式化失败，返回原始字符串
  }
}

const getPdfFileName = (filePath) => {
  if (!filePath) return ''
  try {
    return filePath.split('/').pop() || 'PDF文件'
  } catch (error) {
    return 'PDF文件'
  }
}

// 生命周期
onMounted(() => {
  loadEquipmentMenu()
  loadNotices(true)
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  // 清理缓存
  dateFormatCache.clear()
})
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-top: 46px;
}

// 搜索容器
.search-container {
  background: white;
  padding: 5px 0;
}

// 筛选容器
.filter-container {
  background: white;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #ebedf0;

  .status-tabs {
    flex: 1;

    :deep(.van-tabs__nav) {
      background: transparent;
    }

    :deep(.van-tab) {
      font-size: 14px;
      color: #969799;

      &.van-tab--active {
        color: #e74c3c;
        font-weight: 500;
      }
    }

    :deep(.van-tabs__line) {
      background-color: #e74c3c;
    }
  }

  .equipment-filter-wrapper {
    display: flex;
    align-items: center;
    margin-left: 12px;
    gap: 8px;
    max-width: 180px; // 设置最大宽度限制
  }

  .equipment-filter-btn {
    font-size: 12px;
    color: #969799;
    border-color: #ebedf0;
    min-height: 44px; // 确保触摸友好的最小高度
    flex: 1;
    max-width: 140px; // 筛选按钮最大宽度
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:active {
      background-color: #f7f8fa;
    }

    &.has-filter {
      color: #e74c3c;
      border-color: #e74c3c;
      background-color: rgba(231, 76, 60, 0.05);
    }
  }

  .clear-filter-btn {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    min-height: 32px;
    padding: 0;
    color: #969799;
    border-color: #ebedf0;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      background-color: #f7f8fa;
      color: #e74c3c;
    }

    :deep(.van-icon) {
      font-size: 14px;
    }
  }
}

// 内容容器
.content-container {
  flex: 1;
  padding-top: 8px;
  // padding: 0 16px 16px;
}

// 公告项
.notice-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #ebedf0;

  .notice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .status-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-tag {
        font-size: 12px;
      }

      .notice-time {
        font-size: 12px;
        color: #969799;
      }
    }
  }

  .notice-body {
    .notice-description {
      font-size: 14px;
      line-height: 1.5;
      color: #323233;
      margin-bottom: 12px;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .pdf-file {
      display: flex;
      align-items: center;
      padding: 12px;
      background: #f7f8fa;
      border-radius: 6px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: background-color 0.2s;

      &:active {
        background: #ebedf0;
      }

      .pdf-icon {
        color: #e74c3c;
        margin-right: 8px;
        font-size: 16px;
      }

      .pdf-name {
        flex: 1;
        font-size: 14px;
        color: #323233;
        font-weight: 500;
      }

      .arrow-icon {
        color: #969799;
        font-size: 12px;
      }
    }

    .equipment-info {
      .info-row {
        display: flex;
        margin-bottom: 6px;
        font-size: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          color: #969799;
          margin-right: 8px;
          min-width: 60px;
          flex-shrink: 0;
        }

        .info-value {
          color: #323233;
          flex: 1;
          word-break: break-all;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .content-container {
    padding: 0 12px 12px;
  }

  .notice-item {
    padding: 12px;
    margin-bottom: 8px;

    .notice-body {
      .equipment-info {
        .info-row {
          .info-label {
            min-width: 50px;
            font-size: 11px;
          }

          .info-value {
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>