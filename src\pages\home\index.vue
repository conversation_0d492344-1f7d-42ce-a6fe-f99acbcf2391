<template>
  <div class="home-page">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-tabs">
        <van-tabs v-model:active="activeSearchTab" @change="onSearchTabChange">
          <van-tab :title="$t('home.equipmentSearch')" name="equipment">
            <div class="search-input-wrapper">
              <van-search v-model="searchParams.equipmentKey" :placeholder="$t('home.equipmentSearchPlaceholder')"
                @search="onSearch" @clear="onSearchClear" />
              <van-button type="primary" @click="showFilterPopup = true" icon="filter-o" class="filter-btn"
                size="small">
                <span v-if="hasActiveFilters" class="filter-badge">{{ activeFilterCount }}</span>
              </van-button>
            </div>
          </van-tab>
          <van-tab :title="$t('home.partsSearch')" name="parts">
            <div class="search-input-wrapper">
              <van-search v-model="searchParams.partsKey" :placeholder="$t('home.partsSearchPlaceholder')"
                @search="onSearch" @clear="onSearchClear" />
              <van-button type="primary" @click="showFilterPopup = true" icon="filter-o" class="filter-btn"
                size="small">
                <span v-if="hasActiveFilters" class="filter-badge">{{ activeFilterCount }}</span>
              </van-button>
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup v-model:show="showFilterPopup" position="bottom" :style="{ height: '70%' }" round closeable
      close-icon-position="top-right">
      <div class="filter-popup-content">
        <div class="filter-header">
          <h3 class="filter-title">{{ $t('home.filterConditions') }}</h3>
        </div>

        <div class="filter-content">
          <!-- 品牌选择 -->
          <div class="filter-item">
            <label class="filter-label">{{ $t('home.brand') }}</label>
            <van-field v-model="filterParams.brandName" is-link readonly :placeholder="$t('home.selectBrand')"
              @click="showBrandPicker = true" />
          </div>

          <!-- 设备类型选择 -->
          <div class="filter-item">
            <label class="filter-label">{{ $t('home.equipmentType') }}</label>
            <van-field v-model="filterParams.equipmentTypeName" is-link readonly
              :placeholder="$t('home.selectEquipmentType')" @click="showEquipmentTypePicker = true"
              :disabled="!filterParams.brandId" />
          </div>

          <!-- 设备型号选择 -->
          <div class="filter-item">
            <label class="filter-label">{{ $t('home.equipmentModel') }}</label>
            <van-field v-model="filterParams.modelName" is-link readonly :placeholder="$t('home.selectEquipmentModel')"
              @click="showModelPicker = true" :disabled="!filterParams.equipmentTypeId" />
          </div>

          <!-- 服务代码选择 -->
          <div class="filter-item">
            <label class="filter-label">{{ $t('home.serviceCode') }}</label>
            <van-field v-model="filterParams.serviceCodeName" is-link readonly
              :placeholder="$t('home.selectServiceCode')" @click="showServiceCodePicker = true"
              :disabled="!filterParams.modelId" />
          </div>
        </div>

        <!-- 筛选按钮 -->
        <div class="filter-actions">
          <van-button type="default" @click="resetFilter" class="reset-btn">
            {{ $t('home.reset') }}
          </van-button>
          <van-button type="primary" @click="applyFilter" class="apply-btn">
            {{ $t('home.applyFilter') }}
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 内容展示区域 -->
    <div class="content-section">
      <!-- 设备列表 -->
      <van-list v-if="activeSearchTab === 'equipment'" v-model:loading="equipmentLoading" v-model:error="equipmentError"
        :finished="equipmentFinished" :finished-text="$t('home.noMoreData')" :error-text="$t('home.loadError')"
        :loading-text="$t('home.loading')" @load="loadMoreEquipment" class="equipment-list">
        <EquipmentList :data="equipmentList" :loading="false" :has-more="false" @add-to-cart="addToCart"
          @add-to-favorites="addToFavorites" @view-details="viewEquipmentDetails" @view-bom="viewBomDiagram"
          @navigate-to-detail="navigateToDetail" />
      </van-list>

      <!-- 配件列表 -->
      <van-list v-if="activeSearchTab === 'parts'" v-model:loading="partsLoading" v-model:error="partsError"
        :finished="partsFinished" :finished-text="$t('home.noMoreData')" :error-text="$t('home.loadError')"
        :loading-text="$t('home.loading')" @load="loadMoreParts" class="parts-list">
        <PartsList :data="partsList" :loading="false" :has-more="false" @add-to-cart="addToCart"
          @add-to-favorites="addToFavorites" @view-details="viewPartDetails" @view-bom="viewBomDiagram"
          @navigate-to-detail="navigateToDetail" />
      </van-list>
    </div>

    <!-- 品牌选择器 -->
    <van-popup v-model:show="showBrandPicker" position="bottom">
      <van-picker :columns="brandOptions" @confirm="onBrandConfirm" @cancel="showBrandPicker = false" />
    </van-popup>

    <!-- 设备类型选择器 -->
    <van-popup v-model:show="showEquipmentTypePicker" position="bottom">
      <van-picker :columns="equipmentTypeOptions" @confirm="onEquipmentTypeConfirm"
        @cancel="showEquipmentTypePicker = false" />
    </van-popup>

    <!-- 设备型号选择器 -->
    <van-popup v-model:show="showModelPicker" position="bottom">
      <van-picker :columns="modelOptions" @confirm="onModelConfirm" @cancel="showModelPicker = false" />
    </van-popup>

    <!-- 服务代码选择器 -->
    <van-popup v-model:show="showServiceCodePicker" position="bottom">
      <van-picker :columns="serviceCodeOptions" @confirm="onServiceCodeConfirm"
        @cancel="showServiceCodePicker = false" />
    </van-popup>

    <!-- BOM图表弹窗 - 购物车风格 -->
    <van-popup
      v-model:show="showBomDialog"
      position="bottom"
      :style="{ height: '100%' }"
      @close="handleBomDialogClose"
    >
      <div class="bom-drawer-container">
        <!-- 抽屉头部 -->
        <div class="bom-drawer-header">
          <div class="header-left">
            <span class="drawer-title">{{ t('home.viewBom') }}</span>
            <p class="bom-dialog-subtitle" v-if="bomParams.materielName">
              {{ bomParams.materielName }}
            </p>
          </div>
          <div class="header-right">
            <van-button icon="close" size="small" @click="closeBomDialog" class="close-btn" />
          </div>
        </div>

        <!-- BOM Canvas组件 -->
        <div class="bom-canvas-wrapper">
          <BomCanvas
            v-if="showBomDialog"
            :params="bomParams"
            :target-part="targetPartInfo"
            ref="bomCanvasRef"
          />
        </div>
      </div>

    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { showNotify } from 'vant'
import apiService from '@/utils/api'
import BomCanvas from '@/components/BomCanvas.vue'
import EquipmentList from './components/EquipmentList.vue'
import PartsList from './components/PartsList.vue'

const { t } = useI18n()
const router = useRouter()

// 搜索相关状态
const activeSearchTab = ref('equipment')
const searchParams = reactive({
  equipmentKey: '',
  partsKey: ''
})

// 筛选相关状态
const showFilterPopup = ref(false)
const filterParams = reactive({
  brandId: '',
  brandName: '',
  equipmentTypeId: '',
  equipmentTypeName: '',
  modelId: '',
  modelName: '',
  serviceCodeId: '',
  serviceCodeName: ''
})

// 选择器显示状态
const showBrandPicker = ref(false)
const showEquipmentTypePicker = ref(false)
const showModelPicker = ref(false)
const showServiceCodePicker = ref(false)

// 数据列表
const equipmentList = ref([])
const partsList = ref([])
const loading = ref(false)

// Vant List 状态
const equipmentLoading = ref(false)
const equipmentError = ref(false)
const equipmentFinished = ref(false)

const partsLoading = ref(false)
const partsError = ref(false)
const partsFinished = ref(false)

// 分页参数
const equipmentPage = reactive({
  current: 1,
  size: 10,
  total: 0
})

const partsPage = reactive({
  current: 1,
  size: 300,
  total: 0
})

// 菜单数据
const menuData = ref([])

// BOM弹窗相关
const showBomDialog = ref(false)
const bomParams = reactive({
  id: '',
  existsCart: '',
  existsFavorites: '',
  filePath: '',
  bomId: '',
  materielCode: '',
  materielNameEn: '',
  materielName: '',
  indexNo: 0,
  quantity: 0,
  amount: 0,
  price: 0
})
const bomCanvasRef = ref()

// 新增：目标配件信息，用于自动定位
const targetPartInfo = ref(null)



// 计算属性 - 筛选状态
const hasActiveFilters = computed(() => {
  return filterParams.brandId || filterParams.equipmentTypeId ||
    filterParams.modelId || filterParams.serviceCodeId
})

const activeFilterCount = computed(() => {
  let count = 0
  if (filterParams.brandId) count++
  if (filterParams.equipmentTypeId) count++
  if (filterParams.modelId) count++
  if (filterParams.serviceCodeId) count++
  return count
})

// 计算属性 - 选择器选项
const brandOptions = computed(() => {
  return menuData.value.map(item => ({
    text: item.fileName,
    value: item.id
  }))
})

const equipmentTypeOptions = computed(() => {
  if (!filterParams.brandId) return []
  const brand = menuData.value.find(item => item.id === filterParams.brandId)
  return brand?.children?.map(item => ({
    text: item.fileName,
    value: item.id
  })) || []
})

const modelOptions = computed(() => {
  if (!filterParams.equipmentTypeId) return []
  const brand = menuData.value.find(item => item.id === filterParams.brandId)
  const equipmentType = brand?.children?.find(item => item.id === filterParams.equipmentTypeId)
  return equipmentType?.children?.map(item => ({
    text: item.fileName,
    value: item.id
  })) || []
})

const serviceCodeOptions = computed(() => {
  if (!filterParams.modelId) return []
  const brand = menuData.value.find(item => item.id === filterParams.brandId)
  const equipmentType = brand?.children?.find(item => item.id === filterParams.equipmentTypeId)
  const model = equipmentType?.children?.find(item => item.id === filterParams.modelId)
  return model?.children?.map(item => ({
    text: item.fileName,
    value: item.id
  })) || []
})

// 搜索标签切换
const onSearchTabChange = (name) => {
  activeSearchTab.value = name
  // 切换时不清空数据，保持用户已加载的内容
  // 如果对应标签页没有数据，则自动加载
  if (name === 'equipment' && equipmentList.value.length === 0) {
    searchEquipment()
  } else if (name === 'parts' && partsList.value.length === 0) {
    searchParts()
  }
}

// 搜索功能
const onSearch = () => {
  if (activeSearchTab.value === 'equipment') {
    searchEquipment()
  } else {
    searchParts()
  }
}

// 清空搜索
const onSearchClear = () => {
  if (activeSearchTab.value === 'equipment') {
    searchParams.equipmentKey = ''
    equipmentList.value = []
  } else {
    searchParams.partsKey = ''
    partsList.value = []
  }
}

// 获取当前筛选ID
const getFilterId = () => {
  return filterParams.serviceCodeId ||
    filterParams.modelId ||
    filterParams.equipmentTypeId ||
    filterParams.brandId ||
    '9033c9a9872d4106bf2f49f511210878' // 默认Equipment ID
}

// 搜索设备
const searchEquipment = async () => {
  loading.value = true
  equipmentPage.current = 1
  equipmentFinished.value = false
  equipmentError.value = false

  try {
    const params = {
      parentId: getFilterId(),
      key: searchParams.equipmentKey || '',
      pageNo: equipmentPage.current,
      pageSize: equipmentPage.size
    }

    const response = await apiService.catalog.searchProducts(params)
    equipmentList.value = response.data || []
    equipmentPage.total = response.total || 0

    // 检查是否已加载完所有数据
    if (equipmentList.value.length >= equipmentPage.total) {
      equipmentFinished.value = true
    }
  } catch (error) {
    showNotify({ type: 'danger', message: '搜索失败，请重试' })
    equipmentError.value = true
  } finally {
    loading.value = false
  }
}

// 搜索配件
const searchParts = async () => {
  loading.value = true
  partsPage.current = 1
  partsFinished.value = false
  partsError.value = false

  try {
    // 配件搜索时，如果是顶级品牌（Equipment或Engine），则不传parentId
    let parentId = getFilterId()
    if (parentId === '9033c9a9872d4106bf2f49f511210878' || parentId === '533c4332a0b24a959808a279766bb550') {
      parentId = ''
    }

    const params = {
      parentId,
      key: searchParams.partsKey || '',
      pageNo: partsPage.current,
      pageSize: partsPage.size
    }

    const response = await apiService.catalog.searchSparts(params)
    partsList.value = response.data || []
    partsPage.total = response.total || 0

    // 检查是否已加载完所有数据
    if (partsList.value.length >= partsPage.total) {
      partsFinished.value = true
    }
  } catch (error) {
    showNotify({ type: 'danger', message: '搜索失败，请重试' })
    partsError.value = true
  } finally {
    loading.value = false
  }
}

// 加载更多设备 - 适配 Vant List
const loadMoreEquipment = async () => {
  if (equipmentFinished.value || equipmentLoading.value) return

  equipmentLoading.value = true
  equipmentError.value = false

  try {
    equipmentPage.current++

    const params = {
      parentId: getFilterId(),
      key: searchParams.equipmentKey || '',
      pageNo: equipmentPage.current,
      pageSize: equipmentPage.size
    }

    const response = await apiService.catalog.searchProducts(params)
    const newData = response.data || []

    if (newData.length > 0) {
      equipmentList.value.push(...newData)
    }

    // 检查是否已加载完所有数据
    if (equipmentList.value.length >= equipmentPage.total || newData.length < equipmentPage.size) {
      equipmentFinished.value = true
    }
  } catch (error) {
    equipmentError.value = true
    equipmentPage.current--
  } finally {
    equipmentLoading.value = false
  }
}

// 加载更多配件 - 适配 Vant List
const loadMoreParts = async () => {
  if (partsFinished.value || partsLoading.value) return

  partsLoading.value = true
  partsError.value = false

  try {
    partsPage.current++

    // 配件搜索时，如果是顶级品牌（Equipment或Engine），则不传parentId
    let parentId = getFilterId()
    if (parentId === '9033c9a9872d4106bf2f49f511210878' || parentId === '533c4332a0b24a959808a279766bb550') {
      parentId = ''
    }

    const params = {
      parentId,
      key: searchParams.partsKey || '',
      pageNo: partsPage.current,
      pageSize: partsPage.size
    }

    const response = await apiService.catalog.searchSparts(params)
    const newData = response.data || []

    if (newData.length > 0) {
      partsList.value.push(...newData)
    }

    // 检查是否已加载完所有数据
    if (partsList.value.length >= partsPage.total || newData.length < partsPage.size) {
      partsFinished.value = true
    }
  } catch (error) {
    partsError.value = true
    partsPage.current--
  } finally {
    partsLoading.value = false
  }
}

// 筛选器相关方法
const onBrandConfirm = ({ selectedOptions }) => {
  const selected = selectedOptions[0]
  filterParams.brandId = selected.value
  filterParams.brandName = selected.text

  // 清空下级选择
  resetSubFilters('brand')
  showBrandPicker.value = false
}

const onEquipmentTypeConfirm = ({ selectedOptions }) => {
  const selected = selectedOptions[0]
  filterParams.equipmentTypeId = selected.value
  filterParams.equipmentTypeName = selected.text

  // 清空下级选择
  resetSubFilters('equipmentType')
  showEquipmentTypePicker.value = false
}

const onModelConfirm = ({ selectedOptions }) => {
  const selected = selectedOptions[0]
  filterParams.modelId = selected.value
  filterParams.modelName = selected.text

  // 清空下级选择
  resetSubFilters('model')
  showModelPicker.value = false
}

const onServiceCodeConfirm = ({ selectedOptions }) => {
  const selected = selectedOptions[0]
  filterParams.serviceCodeId = selected.value
  filterParams.serviceCodeName = selected.text
  showServiceCodePicker.value = false
}

// 重置子级筛选器
const resetSubFilters = (level) => {
  switch (level) {
    case 'brand':
      filterParams.equipmentTypeId = ''
      filterParams.equipmentTypeName = ''
    // fall through
    case 'equipmentType':
      filterParams.modelId = ''
      filterParams.modelName = ''
    // fall through
    case 'model':
      filterParams.serviceCodeId = ''
      filterParams.serviceCodeName = ''
      break
  }
}

// 重置筛选器
const resetFilter = () => {
  Object.assign(filterParams, {
    brandId: '',
    brandName: '',
    equipmentTypeId: '',
    equipmentTypeName: '',
    modelId: '',
    modelName: '',
    serviceCodeId: '',
    serviceCodeName: ''
  })
}

// 应用筛选
const applyFilter = () => {
  // 重置分页
  equipmentPage.current = 1
  partsPage.current = 1

  // 执行搜索
  onSearch()

  // 关闭筛选弹窗
  showFilterPopup.value = false
}

// 添加到购物车
const addToCart = async (item) => {
  try {
    item.buyLoad = true
    const response = await apiService.cart.addToCart([{
      amount: item.amount || 1,
      bomItemsId: item.id || item.bomItemsId,
      productType: item.productType || (activeSearchTab.value === 'equipment' ? 'product' : 'part')
    }])

    if (response.code == '0') {
      item.existsCart = '1'
      showNotify({ type: 'success', message: response.msg || '添加成功' })
    } else if (response.code == '1') {
      item.existsCart = '0'
      showNotify({ type: 'warning', message: response.msg || '已从购物车移除' })
    } else {
      showNotify({ type: 'danger', message: response.msg || '操作失败' })
    }
  } catch (error) {
    showNotify({ type: 'danger', message: '操作失败，请重试' })
  } finally {
    item.buyLoad = false
  }
}

// 添加到收藏夹
const addToFavorites = async (item) => {
  try {
    item.favoritLoad = true
    const response = await apiService.cart.addToFavorites([{
      bomItemsId: item.id || item.bomItemsId,
      productType: item.productType || (activeSearchTab.value === 'equipment' ? 'product' : 'part')
    }])

    if (response.code == '0') {
      item.existsFavorites = '1'
      showNotify({ type: 'success', message: response.msg || '收藏成功' })
    } else if (response.code == '1') {
      item.existsFavorites = '0'
      showNotify({ type: 'warning', message: response.msg || '已取消收藏' })
    } else {
      showNotify({ type: 'danger', message: response.msg || '操作失败' })
    }
  } catch (error) {
    showNotify({ type: 'danger', message: '操作失败，请重试' })
  } finally {
    item.favoritLoad = false
  }
}

// 查看设备详情
const viewEquipmentDetails = (item) => {
  // 参考PC端的跳转逻辑，传递设备的filePath作为参数
  if (item.filePath) {
    router.push({
      path: '/detail',
      query: {
        params: encodeURIComponent(item.filePath), // URL编码params值
        // 传递设备的基本信息
        equipmentId: item.id,
        model: item.model,
        serviceCode: item.serviceCode,
        materielCode: item.materielCode,
        materielName: item.materielName,
        materielNameEn: item.materielNameEn,
        price: item.price || item.totalPrice,
        amount: item.amount,
        existsCart: item.existsCart,
        existsFavorites: item.existsFavorites
      }
    })
  } else {
    showNotify({ type: 'warning', message: t('home.noDetailAvailable') })
  }
}

// 查看配件详情
const viewPartDetails = (item) => {
  // 跳转到移动端详情页面
  router.push({
    path: `/detail/${item.id || item.bomItemsId}`,
    query: {
      bomId: item.bomId,
      filePath: item.filePath ? encodeURIComponent(item.filePath) : '', // URL编码filePath值
      materielCode: item.materielCode,
      materielName: item.materielName,
      materielNameEn: item.materielNameEn,
      indexNo: item.indexNo,
      quantity: item.quantity,
      price: item.price,
      existsCart: item.existsCart,
      existsFavorites: item.existsFavorites
    }
  })
}

// 查看BOM图表
const viewBomDiagram = (item) => {
  // 设置BOM参数
  Object.assign(bomParams, {
    id: item.bomItemsId || item.id,
    existsCart: item.existsCart || '0',
    existsFavorites: item.existsFavorites || '0',
    filePath: item.filePath,
    bomId: item.bomId,
    materielCode: item.materielCode,
    materielNameEn: item.materielNameEn,
    materielName: item.materielName,
    indexNo: item.indexNo || 0,
    quantity: item.quantity || 0,
    amount: item.amount || 0,
    price: item.price || 0
  })

  // 新增：设置目标配件信息用于自动定位
  // 如果传入的item包含配件信息，则设置为目标配件进行定位
  if (item.indexNo || item.materielCode || item.id || item.bomItemsId) {
    targetPartInfo.value = {
      indexNo: item.indexNo,
      materielCode: item.materielCode,
      id: item.bomItemsId || item.id,
      bomItemsId: item.bomItemsId,
      materielNameEn: item.materielNameEn,
      materielName: item.materielName
    }
  } else {
    // 如果没有配件信息，清除目标配件
    targetPartInfo.value = null
  }

  showBomDialog.value = true
}

// 新增：关闭BOM弹窗
const closeBomDialog = () => {
  showBomDialog.value = false
  handleBomDialogClose()
}

// 处理BOM弹窗关闭事件
const handleBomDialogClose = () => {
  // 清除目标配件信息，避免下次打开时误触发定位
  targetPartInfo.value = null
}

// 处理图片点击跳转到详情页
const navigateToDetail = (routeParams) => {
  // 使用传入的路由参数进行跳转
  router.push(routeParams)
}

// 获取菜单数据
const getMenuData = async () => {
  try {
    const response = await apiService.system.getMenu()
    menuData.value = response || []

    // 默认选择Equipment品牌
    const defaultBrand = menuData.value.find(item => item.id === '9033c9a9872d4106bf2f49f511210878')
    if (defaultBrand) {
      filterParams.brandId = defaultBrand.id
      filterParams.brandName = defaultBrand.fileName
    }
  } catch (error) {
    // 获取菜单数据失败
  }
}

// 初始化
onMounted(async () => {
  await getMenuData()
  // 初始加载设备数据
  searchEquipment()
})
</script>

<style lang="scss" scoped>
.home-page {
  padding: 0;
  background-color: #fff;
  min-height: 100vh;
}

.search-section {
  background: white;
  padding: 8px;
  margin-bottom: 8px;
  padding-bottom: 0;

  .search-tabs {
    :deep(.van-tabs__nav) {
      background: #f5f5f5;
      border-radius: 8px;
      padding: 4px;
    }

    :deep(.van-tab) {
      border-radius: 6px;
      margin: 0 2px;
    }

    :deep(.van-tab--active) {
      background: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .search-input-wrapper {
    padding-top: 8px;
    // padding: 8px 0 0px;
    display: flex;
    align-items: center;
    gap: 12px;

    .van-search {
      flex: 1;
    }

    .filter-btn {
      flex-shrink: 0;
      width: 28px;
      height: 28px;
      padding: 0;
      position: relative;

      .filter-badge {
        position: absolute;
        top: -6px;
        right: -6px;
        background: #ff4444;
        color: white;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        font-size: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
      }
    }
  }
}

.filter-popup-content {
  height: 100%;
  display: flex;
  flex-direction: column;

  .filter-header {
    padding: 16px 16px 0;
    border-bottom: 1px solid #ebedf0;

    .filter-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      text-align: center;
      padding-bottom: 16px;
    }
  }

  .filter-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .filter-item {
      margin-bottom: 16px;

      .filter-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #323233;
      }
    }
  }

  .filter-actions {
    padding: 16px;
    border-top: 1px solid #ebedf0;
    display: flex;
    gap: 12px;

    .reset-btn,
    .apply-btn {
      flex: 1;
      height: 44px;
    }
  }
}

.content-section {
  flex: 1;
  background: white;
  min-height: 400px;
}

// BOM抽屉样式
.bom-drawer-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.bom-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-left {
    .drawer-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .orientation-btn {
      margin-right: 4px;
    }

    .close-btn {
      color: #666;

      &:hover {
        color: #333;
      }
    }
  }
}

.bom-canvas-wrapper {
  flex: 1;
  overflow: hidden;
}

// 横屏模式下的抽屉适配
@media (orientation: landscape) {
  .bom-drawer-container {
    // 横屏时优化布局
    max-height: 100vh;

    // 确保内容区域充分利用空间
    .bom-drawer-content {
      flex: 1;
      min-height: 0; // 重要：允许flex子项收缩
    }
  }

  .bom-drawer-header {
    // 横屏时减少头部高度
    padding: 6px 16px;
    min-height: 44px;

    .header-left .drawer-title {
      font-size: 16px;
      font-weight: 600;
    }

    .header-right {
      .close-btn {
        padding: 4px 8px;
        min-width: 32px;
        height: 32px;
      }
    }
  }

  // 横屏时BOM Canvas区域优化
  .bom-drawer-content {
    // 移除多余的padding，最大化画布空间
    padding: 8px;

    // 确保BOM Canvas组件能充分利用空间
    :deep(.bom-canvas-container) {
      height: 100%;
      min-height: calc(100vh - 80px); // 减去头部高度
    }
  }
}
</style>