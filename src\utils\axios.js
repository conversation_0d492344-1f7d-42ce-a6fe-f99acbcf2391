import axios from "axios";
import { showNotify } from "vant";
import { BASE_URL } from "@/config/config";
import router from "../router";
import { useUserStore } from "@/stores/user.js";

// 创建axios实例
axios.defaults.withCredentials = true;
const request = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
  withCredentials: true,
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    config.headers = {
      ...config.headers,
      Accept: "*/*",
    };

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    if (response.config.responseType === "blob") {
      return response.data;
    }
    switch (response.status) {
      case 200:
        if (response?.data?.includes?.("未登录或登录超时。请重新登录，谢谢")) {
          sessionStorage.removeItem("userInfo");
          localStorage.removeItem("userInfo");

          const redirectUrl = window.location.origin + "/login";
          window.location.assign(redirectUrl);
          return Promise.reject(new Error(`用户登录失效，请重新登录!`));
        }
        return response.data;
      case 500:
        showNotify({ type: "danger", message: "请求失败!" });
        break;
      default:
        console.error(`意外的状态码: ${response.status}`);
    }
  },
  (error) => {
    console.log("status-err", error);

    // 处理网络错误
    if (error.code === "ERR_NETWORK") {
      const userStore = useUserStore();
      userStore.clearUserInfo();
      router.replace("/login");
      return Promise.reject(error);
    }

    switch (error.response?.status) {
      case 500:
        showNotify({ type: "danger", message: "请求失败!" });
        break;
      default:
        console.error(`意外的状态码: ${error.response?.status}`);
    }
    return Promise.reject(new Error(`意外的状态码: ${error.response?.status}`));
  }
);

/**
 * GET请求
 * @param {string} url - 请求地址
 * @param {Object} params - 请求参数
 * @param {Object} headers - 请求头
 * @param {Object} config - 额外配置
 * @returns {Promise} - 返回响应数据
 */
export const get = async (
  url,
  params,
  headers = {
    "Content-Type": "application/x-www-form-urlencoded",
  },
  config = {}
) => {
  try {
    const response = await request.get(url, { params, headers, ...config });
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * POST请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} headers - 请求头
 * @param {boolean} isparams - 是否将数据转换为URLSearchParams格式
 * @returns {Promise} - 返回响应数据
 */
export const post = async (
  url,
  data,
  headers = {
    "Content-Type": "application/x-www-form-urlencoded",
  },
  isparams = true
) => {
  try {
    const formattedData = isparams ? new URLSearchParams(data).toString() : data;
    const response = await request.post(url, formattedData, { headers });
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * FormData格式的POST请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @returns {Promise} - 返回响应数据
 */
export const postFormData = async (url, data) => {
  try {
    const response = await request.post(url, data, {
      headers: {
        "Content-Type": undefined,
      },
    });
    return response;
  } catch (error) {
    throw error;
  }
};

// 导出请求实例，方便直接使用
export default request;
