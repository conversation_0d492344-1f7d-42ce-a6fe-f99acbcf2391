/**
 * 配置管理组合式函数
 */

import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import config, { 
  getConfigValue, 
  reloadConfig, 
  getCurrentLanguage 
} from '@/config'

/**
 * 配置管理组合式函数
 */
export function useConfig() {
  const { locale } = useI18n()
  
  // 当前配置对象
  const currentConfig = ref(config)
  
  // 当前语言
  const currentLanguage = ref(getCurrentLanguage())
  
  /**
   * 获取配置值
   */
  const getConfig = (path: string, defaultValue: any = null) => {
    return getConfigValue(path, defaultValue, currentConfig.value)
  }
  
  /**
   * 切换语言并重新加载配置
   */
  const switchLanguage = (newLanguage: string) => {
    if (newLanguage !== currentLanguage.value) {
      currentLanguage.value = newLanguage
      reloadConfig(newLanguage)
      currentConfig.value = config

      // 同步更新i18n语言设置（统一使用标准格式）
      if (locale.value !== newLanguage) {
        locale.value = newLanguage
      }
    }
  }
  
  // 监听i18n语言变化，自动同步配置（统一使用标准格式）
  watch(locale, (newLocale: string) => {
    if (newLocale !== currentLanguage.value) {
      switchLanguage(newLocale)
    }
  })
  
  // 计算属性：常用配置的快捷访问
  const languageConfig = computed(() => getConfig('languageConfig', {}))

  return {
    // 响应式数据
    currentConfig,
    currentLanguage,

    // 方法
    getConfig,
    switchLanguage,

    // 计算属性
    languageConfig,
  }
}
