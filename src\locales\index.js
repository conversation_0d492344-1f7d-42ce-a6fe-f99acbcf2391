import { createI18n } from 'vue-i18n'
import en from './en'
import zhCN from './zh-CN'

/**
 * 获取初始语言设置
 * 优先级：localStorage > 配置文件 > 浏览器语言 > 默认语言
 */
function getInitialLocale() {
  // 1. 检查localStorage（优先级最高）
  const storedLang = localStorage.getItem('locale')
  if (storedLang) {
    return storedLang
  }

  // 2. 检查配置文件中的语言设置
  const configLang = import.meta.env?.VITE_LANGUAGE
  if (configLang) {
    return configLang
  }

  // 3. 检查浏览器语言
  const browserLang = navigator.language
  if (browserLang) {
    if (browserLang.startsWith('zh')) {
      return 'zh-CN'
    } else if (browserLang.startsWith('en')) {
      return 'en'
    }
  }

  // 4. 默认语言
  return 'zh-CN'
}

// 创建i18n实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API 模式
  locale: getInitialLocale(), // 使用统一的语言优先级逻辑
  fallbackLocale: 'zh-CN', // 回退语言
  messages: {
    'en': en,
    'zh-CN': zhCN
  },
  globalInjection: true // 全局注入 $t 方法
})

export default i18n 