<template>
  <div class="warranty-page">
    <!-- 页面头部 -->
    <van-sticky>
      <div class="page-header">
        <div class="page-title">{{ $t("warranty.title") }}</div>
        <div class="header-actions">
          <van-button
            icon="plus"
            type="danger"
            size="small"
            round
            @click="registerNewProduct"
            >{{ $t("warranty.registerNew") }}</van-button
          >
          <van-icon name="search" size="20" @click="showSearchPanel = true" />
        </div>
      </div>
    </van-sticky>

    <!-- 搜索面板 -->
    <van-popup
      v-model:show="showSearchPanel"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
      close-icon="close"
      @closed="onSearchPanelClosed"
    >
      <div class="search-panel">
        <div class="search-panel-header">
          <span class="title">{{ $t("warranty.searchTitle") }}</span>
        </div>

        <van-form @submit="onSearch" class="search-form">
          <van-cell-group inset>
            <van-field
              v-model="searchForm.keywords"
              :label="$t('warranty.keywords')"
              :placeholder="$t('warranty.searchCustomer')"
              clearable
            >
              <template #right-icon>
                <van-icon
                  name="clear"
                  v-if="searchForm.keywords"
                  @click="searchForm.keywords = ''"
                />
              </template>
            </van-field>

            <van-field
              readonly
              :label="$t('warranty.startDate')"
              :placeholder="$t('warranty.selectDate')"
              @click="showStartDatePicker = true"
              :model-value="searchForm.startDate"
            >
              <template #right-icon>
                <van-icon name="calendar-o" />
              </template>
            </van-field>

            <van-field
              readonly
              :label="$t('warranty.endDate')"
              :placeholder="$t('warranty.selectDate')"
              @click="showEndDatePicker = true"
              :model-value="searchForm.endDate"
            >
              <template #right-icon>
                <van-icon name="calendar-o" />
              </template>
            </van-field>
          </van-cell-group>

          <div class="form-buttons">
            <van-button
              type="default"
              block
              @click="resetSearch"
              :disabled="!hasActiveFilters"
              >{{ $t("common.reset") }}</van-button
            >
            <van-button type="danger" block native-type="submit">{{
              $t("warranty.filter")
            }}</van-button>
          </div>
        </van-form>
      </div>
    </van-popup>

    <!-- 质保状态标签页 -->
    <van-tabs
      v-model:active="activeTabName"
      @change="handleTabClick"
      sticky
      swipeable
      animated
      :lazy-render="false"
    >
      <van-tab :title="$t('warranty.allRegistrations')" name="all">
        <warranty-list
          :list="allList"
          :loading="loading"
          :finished="finished"
          @refresh="onRefresh"
          @load="onLoad"
          @view-detail="getDetail"
          @confirm="confirm"
          @invalid="invalid"
          @view-claim="detailWarrantyRecord"
          @invalid-claim="invalidItem"
          @add-claim="addWarrantyRecord"
        />
      </van-tab>
      <van-tab :title="$t('warranty.pendingStatus')" name="pending">
        <warranty-list
          :list="pendingList"
          :loading="loading"
          :finished="finished"
          @refresh="onRefresh"
          @load="onLoad"
          @view-detail="getDetail"
          @confirm="confirm"
          @invalid="invalid"
          @view-claim="detailWarrantyRecord"
          @invalid-claim="invalidItem"
          @add-claim="addWarrantyRecord"
        />
      </van-tab>
      <van-tab :title="$t('warranty.confirmedStatus')" name="confirmed">
        <warranty-list
          :list="confirmedList"
          :loading="loading"
          :finished="finished"
          @refresh="onRefresh"
          @load="onLoad"
          @view-detail="getDetail"
          @confirm="confirm"
          @invalid="invalid"
          @view-claim="detailWarrantyRecord"
          @invalid-claim="invalidItem"
          @add-claim="addWarrantyRecord"
        />
      </van-tab>
    </van-tabs>

    <!-- 日期选择器 -->
    <van-popup v-model:show="showStartDatePicker" position="bottom">
      <van-date-picker
        :title="$t('warranty.selectStartDate')"
        @confirm="onStartDateConfirm"
        @cancel="showStartDatePicker = false"
      />
    </van-popup>

    <van-popup v-model:show="showEndDatePicker" position="bottom">
      <van-date-picker
        :title="$t('warranty.selectEndDate')"
        @confirm="onEndDateConfirm"
        @cancel="showEndDatePicker = false"
      />
    </van-popup>

    <!-- 产品注册Dialog -->
    <van-dialog
      v-model:show="showRegisterDialog"
      :title="isViewMode ? $t('warranty.details') : $t('warranty.registerNew')"
      :show-cancel-button="!isViewMode"
      :show-confirm-button="!isViewMode"
      :confirm-button-text="$t('common.submit')"
      :cancel-button-text="$t('common.cancel')"
      @confirm="submitRegister"
      :before-close="beforeCloseRegisterForm"
      class="register-dialog"
      width="80%"
    >
      <template #footer v-if="isViewMode">
        <div class="view-mode-footer">
          <van-button
            block
            type="primary"
            @click="showRegisterDialog = false"
            >{{ $t("common.close") }}</van-button
          >
        </div>
      </template>
      <div class="register-form">
        <!-- 设备信息 -->
        <div class="form-section">
          <h3>{{ $t("warranty.equipmentInfo") }}</h3>
          <van-form ref="registerFormRef">
            <van-field
              v-model="registerForm.brand"
              name="brand"
              :is-link="!isViewMode"
              readonly
              :label="$t('warranty.brandName')"
              :placeholder="$t('warranty.selectBrand')"
              @click="!isViewMode && (showBrandPicker = true)"
              :rules="[{ required: true, message: $t('validation.required') }]"
              required
            />
            <van-field
              v-model="registerForm.productType"
              name="productType"
              :is-link="!isViewMode"
              readonly
              :label="$t('warranty.productType')"
              :placeholder="$t('warranty.selectProductType')"
              @click="!isViewMode && (showEqPicker = true)"
              :rules="[{ required: true, message: $t('validation.required') }]"
              required
            />
            <van-field
              v-model="registerForm.model"
              name="model"
              :is-link="!isViewMode"
              readonly
              :label="$t('warranty.model')"
              :placeholder="$t('warranty.selectModel')"
              @click="!isViewMode && (showModelPicker = true)"
              :rules="[{ required: true, message: $t('validation.required') }]"
              required
            />
            <van-field
              v-model="registerForm.bom"
              name="bom"
              :is-link="!isViewMode"
              readonly
              :label="$t('warranty.serviceCode')"
              :placeholder="$t('warranty.selectServiceCode')"
              @click="!isViewMode && (showVersionPicker = true)"
              :rules="[{ required: true, message: $t('validation.required') }]"
              required
            />
            <van-field
              v-model="registerForm.salesDate"
              name="salesDate"
              :is-link="!isViewMode"
              readonly
              :label="$t('warranty.salesDate')"
              :placeholder="$t('warranty.selectDate')"
              @click="!isViewMode && (showSalesDatePicker = true)"
              :rules="[{ required: true, message: $t('validation.required') }]"
              required
            />
            <van-field
              v-model="registerForm.serialNumber"
              name="serialNumber"
              :readonly="isViewMode"
              :label="$t('warranty.serialNumber')"
              :placeholder="$t('warranty.serialNumber')"
              :rules="[{ required: true, message: $t('validation.required') }]"
              required
            />
            <van-field
              v-model="registerForm.description"
              type="textarea"
              :readonly="isViewMode"
              :label="$t('warranty.note')"
              :placeholder="$t('warranty.note')"
              rows="2"
              autosize
            />

            <!-- 销售收据上传 -->
            <van-field
              name="receipt"
              :label="$t('warranty.uploadPictures')"
              :readonly="true"
              class="upload-field"
              label-align="top"
              :rules="[
                {
                  validator: validateReceiptImages,
                  message: $t('validation.required'),
                },
              ]"
              style="margin-top: 8px; padding-top: 0; width: 100%"
            >
              <template #input>
                <van-uploader
                  v-model="registerForm.pictureList_receipt"
                  multiple
                  :deletable="!isViewMode"
                  :max-count="5"
                  :disabled="isViewMode"
                  :before-read="() => !isViewMode"
                  :after-read="afterReadReceiptImage"
                  :preview-size="70"
                  :preview-image="true"
                  :preview-options="{ showIndex: true }"
                />
              </template>
            </van-field>
          </van-form>
        </div>

        <!-- 客户信息 -->
        <div class="form-section">
          <h3>{{ $t("warranty.customerInfo") }}</h3>
          <div class="customer-selector" v-if="!isViewMode">
            <van-button type="primary" block @click="showCustomerDialog = true">
              {{ $t("warranty.selectCustomer") }}
            </van-button>
          </div>

          <van-field
            v-model="selectedUserForm.linkman"
            readonly
            :label="$t('warranty.name')"
            :placeholder="$t('warranty.name')"
          />
          <van-field
            v-model="selectedUserForm.phone"
            readonly
            :label="$t('warranty.phone')"
            :placeholder="$t('warranty.phone')"
          />
          <van-field
            v-model="selectedUserForm.email"
            readonly
            :label="$t('warranty.email')"
            :placeholder="$t('warranty.email')"
          />
          <van-field
            v-model="selectedUserForm.address"
            readonly
            :label="$t('warranty.address')"
            :placeholder="$t('warranty.address')"
          />
          <van-field
            v-model="selectedUserForm.city"
            readonly
            :label="$t('warranty.city')"
            :placeholder="$t('warranty.city')"
          />
          <van-field
            v-model="selectedUserForm.state"
            readonly
            :label="$t('warranty.state')"
            :placeholder="$t('warranty.state')"
          />
          <van-field
            v-model="selectedUserForm.country"
            readonly
            :label="$t('warranty.country')"
            :placeholder="$t('warranty.country')"
          />
          <van-field
            v-model="selectedUserForm.postalCode"
            readonly
            :label="$t('user.postalCode')"
            :placeholder="$t('user.postalCode')"
          />
        </div>
      </div>
    </van-dialog>

    <!-- 质保索赔弹窗 -->
    <van-dialog
      v-model:show="showWarrantyClaimDialog"
      :title="isViewClaimMode ? $t('warranty.claimDetails') : $t('warranty.addClaimRecord')"
      :show-cancel-button="!isViewClaimMode"
      :show-confirm-button="!isViewClaimMode"
      :confirm-button-text="$t('common.submit')"
      :cancel-button-text="$t('common.cancel')"
      @confirm="submitWarrantyClaim"
      :before-close="beforeCloseWarrantyClaimForm"
      class="claim-dialog"
      width="90%"
    >
      <template #footer v-if="isViewClaimMode">
        <div class="view-mode-footer">
          <van-button
            block
            type="primary"
            @click="showWarrantyClaimDialog = false"
            >{{ $t("common.close") }}</van-button
          >
        </div>
      </template>
      <div class="claim-form">
        <!-- 故障信息区域 -->
        <div class="form-section">
          <h3>{{ $t("warranty.faultInfo") }}</h3>
          <van-form ref="warrantyClaimFormRef">
            <van-field
              v-model="warrantyClaimForm.failureDate"
              name="failureDate"
              :is-link="!isViewClaimMode"
              readonly
              :label="$t('warranty.failureDate')"
              :placeholder="$t('warranty.selectDate')"
              @click="!isViewClaimMode && (showFailureDatePicker = true)"
              :rules="[{ required: true, message: $t('validation.required') }]"
              required
            />
            <van-field
              v-model="warrantyClaimForm.circumstances"
              name="circumstances"
              :readonly="isViewClaimMode"
              :label="$t('warranty.circumstances')"
              :placeholder="$t('warranty.circumstances')"
            />
            <van-field
              v-model="warrantyClaimForm.description"
              name="description"
              type="textarea"
              :readonly="isViewClaimMode"
              :label="$t('warranty.description')"
              :placeholder="$t('warranty.description')"
              rows="4"
              autosize
              :rules="[{ required: true, message: $t('validation.required') }]"
              required
            />

            <!-- 故障图片上传 -->
            <div class="upload-section">
              <van-field
                name="pictures"
                :label="$t('warranty.uploadPictures')"
                :readonly="true"
                :rules="[
                  {
                    validator: validateUploadImages,
                    message: $t('validation.required'),
                  },
                ]"
                required
                class="upload-field"
                label-align="top"
                style="margin-top: 8px; padding-top: 0; width: 100%;"
              >
                <template #input>
                  <van-uploader
                    v-if="!isViewClaimMode"
                    v-model="warrantyClaimForm.pictureList_warranty_claim"
                    multiple
                    :max-count="5"
                    :before-read="beforeAvatarUpload"
                    :after-read="afterReadImage"
                  />
                  <div class="image-preview" v-else-if="claimImages.length > 0">
                    <div 
                      v-for="(img, index) in claimImages" 
                      :key="index"
                      class="image-preview-item"
                      @click="previewImage(img)"
                    >
                      <img :src="img" alt="Claim image" />
                    </div>
                  </div>
                </template>
              </van-field>
            </div>
          </van-form>
        </div>

        <!-- 设备信息区域 -->
        <div class="form-section">
          <h3>{{ $t("warranty.equipmentInfo") }}</h3>
          <van-field
            v-model="warrantyClaimForm.brand"
            readonly
            :label="$t('warranty.brandName')"
          />
          <van-field
            v-model="warrantyClaimForm.productType"
            readonly
            :label="$t('warranty.productType')"
          />
          <van-field
            v-model="warrantyClaimForm.model"
            readonly
            :label="$t('warranty.model')"
          />
          <van-field
            v-model="warrantyClaimForm.serviceCode"
            readonly
            :label="$t('warranty.serviceCode')"
          />
          <van-field
            v-model="warrantyClaimForm.serialNumber"
            readonly
            :label="$t('warranty.serialNumber')"
          />
          <van-field
            v-model="warrantyClaimForm.salesDate"
            readonly
            :label="$t('warranty.salesDate')"
          />
        </div>

        <!-- 客户信息区域 -->
        <div class="form-section">
          <h3>{{ $t("warranty.customerInfo") }}</h3>
          <van-field
            v-model="warrantyClaimForm.linkman"
            readonly
            :label="$t('warranty.name')"
          />
          <van-field
            v-model="warrantyClaimForm.phone"
            readonly
            :label="$t('warranty.phone')"
          />
          <van-field
            v-model="warrantyClaimForm.email"
            readonly
            :label="$t('warranty.email')"
          />
          <van-field
            v-model="warrantyClaimForm.address"
            readonly
            :label="$t('warranty.address')"
          />
          <van-field
            v-model="warrantyClaimForm.city"
            readonly
            :label="$t('warranty.city')"
          />
          <van-field
            v-model="warrantyClaimForm.state"
            readonly
            :label="$t('warranty.state')"
          />
          <van-field
            v-model="warrantyClaimForm.country"
            readonly
            :label="$t('warranty.country')"
          />
          <van-field
            v-model="warrantyClaimForm.postalCode"
            readonly
            :label="$t('user.postalCode')"
          />
        </div>
      </div>
    </van-dialog>

    <!-- 图片预览 -->
    <van-image-preview
      v-model:show="showImagePreview"
      :images="previewImages"
      :start-position="previewIndex"
    />

    <!-- 客户选择/添加Dialog -->
    <van-dialog
      v-model:show="showCustomerDialog"
      :title="$t('warranty.customerInfo')"
      class="customer-dialog"
      :show-confirm-button="false"
      close-on-click-overlay
      close-on-popstate
    >
      <div class="customer-dialog-content">
        <!-- 搜索框 -->
        <van-search
          v-model="searchQuery_customer"
          :placeholder="$t('warranty.searchCustomer')"
          @search="searchCustomers"
          show-action
          @cancel="showCustomerDialog = false"
        />

        <!-- 客户列表 -->
        <div class="customer-list">
          <van-cell-group>
            <van-cell
              v-for="(customer, index) in customers"
              :key="index"
              clickable
              @click="selectCustomer(customer)"
            >
              <template #title>
                <div class="customer-item">
                  <div class="customer-name">{{ customer.linkman }}</div>
                  <div class="customer-info">
                    <div>{{ customer.phone }}</div>
                    <div>{{ customer.email }}</div>
                    <div>{{ customer.address }}</div>
                  </div>
                </div>
              </template>
            </van-cell>
          </van-cell-group>

          <van-empty
            v-if="customers.length === 0"
            :description="$t('warranty.noData')"
          />

          <div class="customer-actions">
            <van-button type="primary" @click="createNewCustomer">{{
              $t("warranty.newCustomer")
            }}</van-button>
            <van-button plain @click="showCustomerDialog = false">{{
              $t("common.cancel")
            }}</van-button>
          </div>
        </div>
      </div>
    </van-dialog>

    <!-- 新建/编辑客户Dialog -->
    <van-dialog
      v-model:show="showCustomerForm"
      :title="
        formCustomer.id
          ? $t('warranty.editCustomer')
          : $t('warranty.newCustomer')
      "
      :confirm-button-text="$t('common.submit')"
      :cancel-button-text="$t('common.cancel')"
      show-cancel-button
      @confirm="saveCustomer"
      :before-close="beforeCloseCustomerForm"
    >
      <div class="customer-form">
        <van-form ref="customerFormRef">
          <van-field
            v-model="formCustomer.linkman"
            name="linkman"
            :label="$t('warranty.name')"
            :placeholder="$t('warranty.name')"
            :rules="[{ required: true, message: $t('validation.required') }]"
            required
          />
          <van-field
            v-model="formCustomer.phone"
            name="phone"
            :label="$t('warranty.phone')"
            :placeholder="$t('warranty.phone')"
            :rules="[{ required: true, message: $t('validation.required') }]"
            required
          />
          <van-field
            v-model="formCustomer.email"
            name="email"
            :label="$t('warranty.email')"
            :placeholder="$t('warranty.email')"
            :rules="[{ required: true, message: $t('validation.required') }]"
            required
          />
          <van-field
            v-model="formCustomer.address"
            name="address"
            :label="$t('warranty.address')"
            :placeholder="$t('warranty.address')"
            :rules="[{ required: true, message: $t('validation.required') }]"
            required
          />
          <van-field
            v-model="formCustomer.city"
            name="city"
            :label="$t('warranty.city')"
            :placeholder="$t('warranty.city')"
            :rules="[{ required: true, message: $t('validation.required') }]"
            required
          />
          <van-field
            v-model="formCustomer.state"
            :label="$t('warranty.state')"
            :placeholder="$t('warranty.state')"
          />
          <van-field
            v-model="formCustomer.country"
            :label="$t('warranty.country')"
            :placeholder="$t('warranty.country')"
          />
          <van-field
            v-model="formCustomer.postalCode"
            :label="$t('user.postalCode')"
            :placeholder="$t('user.postalCode')"
          />
        </van-form>
      </div>
    </van-dialog>

    <!-- 各种Picker -->
    <van-popup v-model:show="showBrandPicker" position="bottom">
      <van-picker
        :columns="getBrandColumns()"
        @confirm="onBrandConfirm"
        @cancel="showBrandPicker = false"
      />
    </van-popup>

    <van-popup v-model:show="showEqPicker" position="bottom">
      <van-picker
        :columns="getEqColumns()"
        @confirm="onEqConfirm"
        @cancel="showEqPicker = false"
      />
    </van-popup>

    <van-popup v-model:show="showModelPicker" position="bottom">
      <van-picker
        :columns="getModelColumns()"
        @confirm="onModelConfirm"
        @cancel="showModelPicker = false"
      />
    </van-popup>

    <van-popup v-model:show="showVersionPicker" position="bottom">
      <van-picker
        :columns="getVersionColumns()"
        @confirm="onVersionConfirm"
        @cancel="showVersionPicker = false"
      />
    </van-popup>

    <van-popup v-model:show="showSalesDatePicker" position="bottom">
      <van-date-picker
        :title="$t('warranty.selectSalesDate')"
        @confirm="onSalesDateConfirm"
        @cancel="showSalesDatePicker = false"
      />
    </van-popup>

    <van-popup v-model:show="showFailureDatePicker" position="bottom">
      <van-date-picker
        :title="$t('warranty.selectFailureDate')"
        @confirm="onFailureDateConfirm"
        @cancel="showFailureDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, inject, computed } from "vue";
import { useRouter } from "vue-router";
import { showToast, showConfirmDialog, showLoadingToast, closeToast } from "vant";
import { useI18n } from "vue-i18n";
import WarrantyList from "./components/WarrantyList.vue";
import apiService from "@/utils/api";

const router = useRouter();
const { t } = useI18n();
const baseDownFileByPath = inject("$base_downFileByPath_url");

// 表单引用
const customerFormRef = ref(null);
const registerFormRef = ref(null);
const warrantyClaimFormRef = ref(null);

// 页面状态
const loading = ref(false);
const refreshing = ref(false);
const finished = ref(false);
// 各标签页数据和分页信息
const allList = ref([]);
const pendingList = ref([]);
const confirmedList = ref([]);
const pageInfoAll = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});
const pageInfoPending = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});
const pageInfoConfirmed = ref({
  pageNo: 1,
  pageSize: 10,
  total: 0
});
const showStartDatePicker = ref(false);
const showEndDatePicker = ref(false);
const showRegisterDialog = ref(false);
const showWarrantyClaimDialog = ref(false);
const showCustomerDialog = ref(false);
const showCustomerForm = ref(false);
const showBrandPicker = ref(false);
const showEqPicker = ref(false);
const showModelPicker = ref(false);
const showVersionPicker = ref(false);
const showSalesDatePicker = ref(false);
const showFailureDatePicker = ref(false);
const showSearchPanel = ref(false);
const searchApplied = ref(false);

// 视图模式状态 (true: 只读, false: 可编辑)
const isViewMode = ref(false);
const isViewClaimMode = ref(false);

// 选项卡状态
const activeTabName = ref("all");

// 查询参数
const params = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0,
  id: "",
  brand: "",
  productType: "",
  model: "",
  bom: "",
});

// 搜索表单
const searchForm = reactive({
  keywords: "",
  startDate: "",
  endDate: "",
  status: "",
});

// 菜单数据
const menu = ref([]);

// 客户相关数据
const customers = ref([]);
const searchQuery_customer = ref("");
const paramsCustomers = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0,
});

// 产品注册表单
const registerForm = reactive({
  id: "",
  brandId: "",
  eqId: "",
  modelId: "",
  versionId: "",
  brand: "",
  productType: "",
  model: "",
  bom: "",
  serialNumber: "",
  salesDate: "",
  description: "",
  pictureList_receipt: [],
  companyLinkmanId: "",
});

// 选择的客户表单
const selectedUserForm = reactive({
  id: "",
  linkman: "",
  companyName: "",
  phone: "",
  email: "",
  address: "",
  city: "",
  state: "",
  country: "",
  postalCode: "",
});

// 编辑中的客户表单
const formCustomer = reactive({
  id: "",
  linkman: "",
  company: "",
  phone: "",
  email: "",
  address: "",
  city: "",
  state: "",
  country: "",
  postalCode: "",
});

// 质保索赔表单
const warrantyClaimForm = reactive({
  registerProductId: "",
  failureDate: "",
  circumstances: "",
  description: "",
  pictureList_warranty_claim: [],
  brand: "",
  productType: "",
  model: "",
  serviceCode: "",
  serialNumber: "",
  salesDate: "",
  linkman: "",
  phone: "",
  email: "",
  address: "",
  city: "",
  state: "",
  country: "",
  postalCode: "",
});

// 是否有活跃筛选
const hasActiveFilters = computed(() => {
  return (
    searchForm.keywords ||
    searchForm.startDate ||
    searchForm.endDate ||
    searchForm.status
  );
});

// 获取当前标签页的列表数据
const getCurrentList = () => {
  switch (activeTabName.value) {
    case 'pending':
      return pendingList;
    case 'confirmed':
      return confirmedList;
    default:
      return allList;
  }
};

// 获取当前标签页的分页信息
const getCurrentPageInfo = () => {
  switch (activeTabName.value) {
    case 'pending':
      return pageInfoPending;
    case 'confirmed':
      return pageInfoConfirmed;
    default:
      return pageInfoAll;
  }
};

// 获取列表数据
const getList = () => {
  loading.value = true;
  
  // 获取当前标签页的分页信息
  const currentPageInfo = getCurrentPageInfo();
  const currentList = getCurrentList();
  
  // 合并查询参数
  const queryParams = {
    pageNo: currentPageInfo.value.pageNo,
    pageSize: currentPageInfo.value.pageSize,
    ...searchForm
  };
  
  // 根据当前选中的标签页设置状态过滤
  if (activeTabName.value === "pending") {
    queryParams.status = "1";
  } else if (activeTabName.value === "confirmed") {
    queryParams.status = "2";
  }
  
  apiService.productRegister.getRegisterList(queryParams)
    .then((res) => {
      // 如果是刷新或第一页，则直接替换数据
      if (refreshing.value || currentPageInfo.value.pageNo === 1) {
        currentList.value = res.data || [];
      } else {
        // 否则追加数据
        currentList.value = [...currentList.value, ...(res.data || [])];
      }
      
      currentPageInfo.value.total = res.total || 0;
      
      // 判断是否加载完成
      if (currentList.value.length >= currentPageInfo.value.total || res.data?.length === 0) {
        finished.value = true;
      } else {
        finished.value = false;
      }
      
      loading.value = false;
      refreshing.value = false;
    })
    .catch(() => {
      showToast(t("common.error"));
      loading.value = false;
      refreshing.value = false;
      finished.value = true; // 出错时也标记为加载完成，避免继续请求
    });
};

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true;
  getCurrentPageInfo().value.pageNo = 1;
  finished.value = false;
  getList();
};

// 上拉加载更多
const onLoad = () => {
  if (!refreshing.value) {
    getCurrentPageInfo().value.pageNo += 1;
    getList();
  }
};

// 搜索相关
const onSearchPanelClosed = () => {
  // 可以在这里处理搜索面板关闭后的逻辑
};

const onSearch = () => {
  // 重置所有列表的页码
  pageInfoAll.value.pageNo = 1;
  pageInfoPending.value.pageNo = 1;
  pageInfoConfirmed.value.pageNo = 1;
  
  // 清空所有列表数据
  allList.value = [];
  pendingList.value = [];
  confirmedList.value = [];
  
  finished.value = false;
  showSearchPanel.value = false;
  getList();
};

// 重置搜索条件
const resetSearch = () => {
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = "";
  });
};

// Tab切换
const handleTabClick = (name) => {
  // 切换标签页时不重置数据，只在需要时加载
  activeTabName.value = name;
  
  // 如果当前标签页的数据为空，则加载数据
  const currentList = getCurrentList();
  if (currentList.value.length === 0) {
    finished.value = false;
    loading.value = false;
    refreshing.value = false;
    getList();
  }
};

// 日期选择
const onStartDateConfirm = (value) => {
  const { selectedValues } = value;
  // 确保月份和日期是两位数格式
  const year = selectedValues[0];
  const month = selectedValues[1].padStart(2, "0");
  const day = selectedValues[2].padStart(2, "0");
  searchForm.startDate = `${year}-${month}-${day}`;
  showStartDatePicker.value = false;
};

const onEndDateConfirm = (value) => {
  const { selectedValues } = value;
  // 确保月份和日期是两位数格式
  const year = selectedValues[0];
  const month = selectedValues[1].padStart(2, "0");
  const day = selectedValues[2].padStart(2, "0");
  searchForm.endDate = `${year}-${month}-${day}`;
  showEndDatePicker.value = false;
};

const onSalesDateConfirm = (value) => {
  const { selectedValues } = value;
  // 确保月份和日期是两位数格式
  const year = selectedValues[0];
  const month = selectedValues[1].padStart(2, "0");
  const day = selectedValues[2].padStart(2, "0");
  registerForm.salesDate = `${year}-${month}-${day}`;
  showSalesDatePicker.value = false;
};

const onFailureDateConfirm = (value) => {
  const { selectedValues } = value;
  // 确保月份和日期是两位数格式
  const year = selectedValues[0];
  const month = selectedValues[1].padStart(2, "0");
  const day = selectedValues[2].padStart(2, "0");
  warrantyClaimForm.failureDate = `${year}-${month}-${day}`;
  showFailureDatePicker.value = false;
};

// 获取菜单选项
const getBrandColumns = () => {
  return menu.value.map((item) => ({
    text: item.fileName,
    value: item.id,
  }));
};

const getEqColumns = () => {
  if (!registerForm.brandId) return [];

  const brandList = menu.value;
  let eqList = [];

  brandList.forEach((brand) => {
    if (brand.id === registerForm.brandId && brand.children) {
      eqList = brand.children;
    }
  });

  return eqList.map((item) => ({
    text: item.fileName,
    value: item.id,
  }));
};

const getModelColumns = () => {
  if (!registerForm.eqId) return [];

  const brandList = menu.value;
  let modelList = [];

  for (const brand of brandList) {
    if (brand.children) {
      for (const eq of brand.children) {
        if (eq.id === registerForm.eqId && eq.children) {
          modelList = eq.children;
          break;
        }
      }
    }
  }

  return modelList.map((item) => ({
    text: item.fileName,
    value: item.id,
  }));
};

const getVersionColumns = () => {
  if (!registerForm.modelId) return [];

  const brandList = menu.value;
  let versionList = [];

  for (const brand of brandList) {
    if (brand.children) {
      for (const eq of brand.children) {
        if (eq.children) {
          for (const model of eq.children) {
            if (model.id === registerForm.modelId && model.children) {
              versionList = model.children;
              break;
            }
          }
        }
      }
    }
  }

  return versionList.map((item) => ({
    text: item.fileName,
    value: item.id,
  }));
};

const onBrandConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0];
  registerForm.brandId = option.value;
  registerForm.brand = option.text;

  // 清除后续选择
  registerForm.eqId = "";
  registerForm.productType = "";
  registerForm.modelId = "";
  registerForm.model = "";
  registerForm.versionId = "";
  registerForm.bom = "";

  showBrandPicker.value = false;
};

const onEqConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0];
  registerForm.eqId = option.value;
  registerForm.productType = option.text;

  // 清除后续选择
  registerForm.modelId = "";
  registerForm.model = "";
  registerForm.versionId = "";
  registerForm.bom = "";

  showEqPicker.value = false;
};

const onModelConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0];
  registerForm.modelId = option.value;
  registerForm.model = option.text;

  // 清除后续选择
  registerForm.versionId = "";
  registerForm.bom = "";

  showModelPicker.value = false;
};

const onVersionConfirm = ({ selectedOptions }) => {
  const option = selectedOptions[0];
  registerForm.versionId = option.value;
  registerForm.bom = option.text;

  showVersionPicker.value = false;
};

// 注册新产品
const registerNewProduct = () => {
  // 清空表单
  Object.keys(registerForm).forEach((key) => {
    registerForm[key] = "";
  });
  registerForm.pictureList_receipt = [];

  Object.keys(selectedUserForm).forEach((key) => {
    selectedUserForm[key] = "";
  });

  // 设置为编辑模式
  isViewMode.value = false;
  showRegisterDialog.value = true;
};

// 查看详情
const getDetail = (item) => {
  // 复制数据到注册表单
  Object.keys(registerForm).forEach((key) => {
    if (key in item) {
      registerForm[key] = item[key];
    }
  });

  // 处理图片
  registerForm.pictureList_receipt = [];
  if (item.files) {
    const fileList = item.files.split(",");
    fileList.forEach((file, index) => {
      const imageUrl = `${baseDownFileByPath}${file}`;
      registerForm.pictureList_receipt.push({
        url: imageUrl,
        isImage: true,
        name: `image-${index}.jpg`,
        status: "done",
      });
    });
  }

  // 复制客户数据
  Object.keys(selectedUserForm).forEach((key) => {
    if (key in item) {
      selectedUserForm[key] = item[key];
    }
  });

  // 设置为只读模式
  isViewMode.value = true;

  // 打开弹窗
  showRegisterDialog.value = true;
};

// 确认产品注册
const confirm = (item) => {
  showConfirmDialog({
    title: t("warranty.confirmTitle"),
    message: t("warranty.confirmMessage"),
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
  }).then(() => {
    apiService.productRegister.confirmRegisterProduct({ id: item.id })
      .then((res) => {
        if (res && res.code == 0) {
          item.status = "2";
          showToast(t("warranty.operateSuccess"));
        } else {
          showToast(t("warranty.operateFailed"));
        }
      })
      .catch(() => {
        showToast(t("common.error"));
      });
  });
};

// 作废产品注册
const invalid = (item) => {
  showConfirmDialog({
    title: t("warranty.invalidTitle"),
    message: t("warranty.invalidMessage"),
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
  }).then(() => {
    apiService.productRegister.deleteRegisterProduct({ id: item.id })
      .then((res) => {
        if (res && res.code == 0) {
          showToast(t("warranty.operateSuccess"));
          refreshing.value = true;
          onRefresh();
        } else {
          showToast(t("warranty.operateFailed"));
        }
      })
      .catch(() => {
        showToast(t("common.error"));
      });
  });
};

// 作废索赔记录
const invalidItem = (item) => {
  showConfirmDialog({
    title: t("warranty.invalidClaimTitle"),
    message: t("warranty.invalidClaimMessage"),
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
  }).then(() => {
    apiService.productRegister.deleteClaimsRecord({ id: item.id })
      .then((res) => {
        if (res && res.code == 0) {
          showToast(t("warranty.operateSuccess"));
          refreshing.value = true;
          onRefresh();
        } else {
          showToast(t("warranty.operateFailed"));
        }
      })
      .catch(() => {
        showToast(t("common.error"));
      });
  });
};

// 查看质保索赔详情
const detailWarrantyRecord = (item, date) => {
  // 复制数据到表单
  Object.keys(warrantyClaimForm).forEach((key) => {
    warrantyClaimForm[key] = "";
  });

  // 设备信息先从主记录获取
  warrantyClaimForm.registerProductId = item.id;
  warrantyClaimForm.brand = item.brand || "";
  warrantyClaimForm.productType = item.productType || "";
  warrantyClaimForm.model = item.model || "";
  warrantyClaimForm.serviceCode = item.bom || "";
  warrantyClaimForm.serialNumber = item.serialNumber || "";
  warrantyClaimForm.salesDate = item.salesDate || "";

  // 客户信息从主记录获取
  warrantyClaimForm.linkman = item.linkman || "";
  warrantyClaimForm.phone = item.phone || "";
  warrantyClaimForm.email = item.email || "";
  warrantyClaimForm.address = item.address || "";
  warrantyClaimForm.city = item.city || "";
  warrantyClaimForm.state = item.state || "";
  warrantyClaimForm.country = item.country || "";
  warrantyClaimForm.postalCode = item.postalCode || "";

  // 索赔记录信息从date参数获取
  if (date) {
    warrantyClaimForm.id = date.id;
    warrantyClaimForm.failureDate = date.failureDate || "";
    warrantyClaimForm.circumstances = date.circumstances || "";
    warrantyClaimForm.description = date.description || "";
  }

  // 处理图片
  warrantyClaimForm.pictureList_warranty_claim = [];
  claimImages.value = [];
  if (date && date.files) {
    const fileList = date.files.split(",");
    fileList.forEach((file) => {
      const imageUrl = `${baseDownFileByPath}${file}`;
      claimImages.value.push(imageUrl);
    });
  }

  // 设置为查看模式
  isViewClaimMode.value = true;
  showWarrantyClaimDialog.value = true;
};

// 添加质保索赔记录
const addWarrantyRecord = (item) => {
  // 清空表单
  Object.keys(warrantyClaimForm).forEach((key) => {
    warrantyClaimForm[key] = "";
  });
  warrantyClaimForm.pictureList_warranty_claim = [];
  claimImages.value = [];

  // 复制设备和客户信息
  warrantyClaimForm.registerProductId = item.id;
  warrantyClaimForm.brand = item.brand || "";
  warrantyClaimForm.productType = item.productType || "";
  warrantyClaimForm.model = item.model || "";
  warrantyClaimForm.serviceCode = item.bom || "";
  warrantyClaimForm.serialNumber = item.serialNumber || "";
  warrantyClaimForm.salesDate = item.salesDate || "";
  warrantyClaimForm.linkman = item.linkman || "";
  warrantyClaimForm.phone = item.phone || "";
  warrantyClaimForm.email = item.email || "";
  warrantyClaimForm.address = item.address || "";
  warrantyClaimForm.city = item.city || "";
  warrantyClaimForm.state = item.state || "";
  warrantyClaimForm.country = item.country || "";
  warrantyClaimForm.postalCode = item.postalCode || "";

  // 设置为编辑模式
  isViewClaimMode.value = false;
  showWarrantyClaimDialog.value = true;
};

// 图片预览
const showImagePreview = ref(false);
const previewImages = ref([]);
const previewIndex = ref(0);
const claimImages = ref([]);

const previewImage = (imageUrl) => {
  previewImages.value = [imageUrl];
  previewIndex.value = 0;
  showImagePreview.value = true;
};

// 检查图片上传
const beforeAvatarUpload = (file) => {
  // 检查文件类型
  if (!['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
    showToast($t('warranty.onlyImageAllowed'));
    return false;
  }
  // 检查文件大小 (小于 5MB)
  if (file.size > 5 * 1024 * 1024) {
    showToast($t('warranty.imageSizeLimit'));
    return false;
  }
  return true;
};

const afterReadImage = (file) => {
  console.log("销售收据图片上传成功", file);
};

// 搜索客户
const searchCustomers = () => {
  // 实现搜索客户的逻辑
};

// 选择客户
const selectCustomer = (customer) => {
  // 实现选择客户的逻辑
};

// 创建新客户
const createNewCustomer = () => {
  // 实现创建新客户的逻辑
};

// 保存客户
const saveCustomer = () => {
  // 实现保存客户的逻辑
};

// 关闭客户对话框
const beforeCloseCustomerForm = () => {
  // 可以在这里处理关闭前的逻辑
};

// 自定义图片验证函数
const validateUploadImages = () => {
  // 检查是否已上传图片
  return (
    warrantyClaimForm.pictureList_warranty_claim &&
    warrantyClaimForm.pictureList_warranty_claim.length > 0 &&
    warrantyClaimForm.pictureList_warranty_claim.some(file => file.file)
  );
};

// 提交质保索赔
const submitWarrantyClaim = () => {
  // 验证表单
  return warrantyClaimFormRef.value
    .validate()
    .then(() => {
      // 验证图片是否已上传
      if (!validateUploadImages()) {
        showToast(t("warranty.pleaseUploadImages"));
        return Promise.reject();
      }

      // 创建表单数据
      const formData = new FormData();

      // 添加所有表单数据
      const data = { ...warrantyClaimForm };
      // 将每个属性和值添加到 formData 中
      for (const key in data) {
        if (data.hasOwnProperty(key) && data[key] !== undefined && data[key] !== null) {
          formData.append(key, data[key]);
        }
      }

      // 添加图片
      if (
        warrantyClaimForm.pictureList_warranty_claim &&
        warrantyClaimForm.pictureList_warranty_claim.length > 0
      ) {
        warrantyClaimForm.pictureList_warranty_claim.forEach((file) => {
          if (file.file) {
            formData.append(`file`, file.file);
          }
        });
      }

      // 显示加载提示
      const loadingToast = showLoadingToast({
        message: t("common.loading"),
        forbidClick: true,
        duration: 0,
      });

      // 提交
      return apiService.productRegister.saveClaimsRecord(formData)
        .then((res) => {
          closeToast();
          if (res && res.code == 0) {
            showToast(t("warranty.operateSuccess"));
            showWarrantyClaimDialog.value = false;
            refreshing.value = true;
            onRefresh();
            return true;
          } else {
            showToast(t("warranty.operateFailed"));
            return false;
          }
        })
        .catch(() => {
          closeToast();
          showToast(t("common.error"));
          return false;
        });
    })
    .catch(() => {
      return false;
    });
};

// 关闭质保索赔对话框
const beforeCloseWarrantyClaimForm = (action) => {
  // 如果是取消操作或查看模式，直接关闭
  if (action === "cancel" || isViewClaimMode.value) {
    return true;
  }

  // 如果是确认操作，不需要再次调用submitWarrantyClaim
  if (action === "confirm") {
    return true;
  }

  // 其他情况，需要验证表单
  return new Promise((resolve) => {
    // 验证表单
    warrantyClaimFormRef.value
      .validate()
      .then(() => {
        // 验证图片上传
        if (!validateUploadImages()) {
          showToast(t("warranty.pleaseUploadImages"));
          resolve(false);
          return;
        }
        resolve(true);
      })
      .catch(() => {
        // 表单验证失败，不关闭弹窗
        resolve(false);
      });
  });
};

// 初始化
onMounted(() => {
  // 初始加载当前标签页的数据
  getList();
  
  // 获取菜单数据
  apiService.system.getMenu().then((res) => {
    menu.value = res;
  });
});
</script>

<style lang="scss" scoped>
.warranty-page {
  min-height: 100vh;
  background-color: #f7f8fa;

  // 页面头部样式
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);

    .page-title {
      font-size: 16px;
      font-weight: bold;
      color: #323233;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  // 视图模式样式
  .view-mode-footer {
    padding: 16px;
  }

  // 搜索面板样式
  .search-panel {
    padding: 16px;

    .search-panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .search-count {
        .count {
          color: #ee0a24;
          font-weight: bold;
        }
      }
    }

    .form-buttons {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-top: 16px;
    }
  }

  // 标签页样式
  :deep(.van-tabs) {
    .van-tab {
      flex: 1;
      font-size: 14px;
      height: 38px;
      line-height: 38px;
    }

    .van-tabs__line {
      background-color: #ee0a24;
    }

    .van-tabs__content {
      padding-top: 0;
    }
  }

  // 对话框样式
  :deep(.register-dialog),
  :deep(.claim-dialog),
  :deep(.customer-dialog) {
    .register-form,
    .claim-form,
    .customer-form {
      padding: 16px;
      max-height: 70vh;
      overflow-y: auto;
    }

    .form-section {
      margin-bottom: 0px;
      h3 {
        margin: 10px 0;
        font-size: 16px;
        color: #323233;
        border-left: 3px solid #ee0a24;
        padding-left: 8px;
      }

      // 确保上传组件显示3列图片
      :deep(.van-uploader) {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        .van-uploader__wrapper {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
        }

        .van-uploader__preview {
          margin: 0 8px 8px 0;
        }
      }
    }

    .customer-selector {
      margin-bottom: 16px;
    }

    // 必填项标识样式
    .van-field--required .van-field__label::before {
      content: "*";
      color: #ee0a24;
      margin-right: 4px;
      font-size: 14px;
      font-family: SimSun, sans-serif;
    }

    .upload-section {
      margin-top: 0;

      .uploader-container {
        margin: 8px 16px 16px;
      }
    }
  }

  .customer-dialog-content {
    max-height: 70vh;
    overflow-y: auto;

    .customer-list {
      padding: 0 12px 12px;

      .customer-item {
        .customer-name {
          font-weight: bold;
          margin-bottom: 6px;
        }

        .customer-info {
          font-size: 13px;
          color: #666;

          > div {
            margin-bottom: 2px;
          }
        }
      }

      .customer-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 12px;
      }
    }
  }

  .claim-dialog {
    .claim-form {
      padding: 0 16px;
      
      .form-section {
        margin-bottom: 16px;
        
        h3 {
          font-size: 16px;
          font-weight: 500;
          color: #323233;
          margin: 16px 0 8px;
        }
      }
      
      .upload-section {
        margin-top: 16px;
        
        .image-preview {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .image-preview-item {
            width: 80px;
            height: 80px;
            overflow: hidden;
            border-radius: 4px;
            border: 1px solid #ebedf0;
            cursor: pointer;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }
      
      .view-mode-footer {
        padding: 10px 16px;
      }
    }
  }
}
</style>