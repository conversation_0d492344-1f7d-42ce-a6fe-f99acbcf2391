<template>
  <router-view v-slot="{ Component }">
    <component :is="Component" />
  </router-view>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

onMounted(() => {
  // 重定向到购物车页面，并通过路由参数区分是收藏页面
  router.replace({ path: '/cart', query: { type: 'favor' } });
});
</script>

<style lang="scss" scoped>
.favor-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .content {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 100px;
    
    .add-button {
      margin-top: 20px;
      width: 120px;
    }
  }
  
  .delete-button {
    height: 100%;
  }
  
  :deep(.van-cell-group__title) {
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
  }
}
</style> 