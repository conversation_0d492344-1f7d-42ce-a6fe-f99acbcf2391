<template>
  <div class="default-layout">
    <div class="content">
      <router-view />
    </div>
    
    <!-- 底部导航栏 -->
    <van-tabbar v-model="activeTab" route>
      <van-tabbar-item name="home" icon="home-o" to="/home" replace>{{ $t('common.home') }}</van-tabbar-item>
      <van-tabbar-item name="catalog" icon="apps-o" to="/catalog" replace>{{ $t('common.catalog') }}</van-tabbar-item>
      <van-tabbar-item name="cart" icon="cart-o" to="/cart" replace>{{ $t('common.cart') }}</van-tabbar-item>
      <van-tabbar-item name="order" icon="orders-o" to="/order" replace>{{ $t('common.order') }}</van-tabbar-item>
      <van-tabbar-item name="user" icon="user-o" to="/user" replace>{{ $t('common.user') }}</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const activeTab = ref('home')

// 监听路由变化，更新激活的标签页
watch(() => route.path, (path) => {
  if (path === '/home') {
    activeTab.value = 'home'
  } else if (path.startsWith('/catalog')) {
    activeTab.value = 'catalog'
  } else if (path.startsWith('/cart')) {
    activeTab.value = 'cart'
  } else if (path.startsWith('/order')) {
    activeTab.value = 'order'
  } else if (path.startsWith('/user')) {
    activeTab.value = 'user'
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.default-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  
  .content {
    flex: 1;
    overflow: auto;
    padding: 0;
    -webkit-overflow-scrolling: touch;
  }
}
</style> 