@use 'variables' as *;

// 弹性布局混合
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// 绝对定位混合
@mixin absolute($top: auto, $right: auto, $bottom: auto, $left: auto) {
  position: absolute;
  top: $top;
  right: $right;
  bottom: $bottom;
  left: $left;
}

// 固定定位混合
@mixin fixed($top: auto, $right: auto, $bottom: auto, $left: auto) {
  position: fixed;
  top: $top;
  right: $right;
  bottom: $bottom;
  left: $left;
}

// 多行文本省略
@mixin ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
  }
}

// 横屏媒体查询
@mixin landscape {
  @media (orientation: landscape) {
    @content;
  }
}

// 竖屏媒体查询
@mixin portrait {
  @media (orientation: portrait) {
    @content;
  }
}

// 响应式断点
@mixin responsive($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: $breakpoint-xs) {
      @content;
    }
  } @else if $breakpoint == sm {
    @media (min-width: $breakpoint-xs) and (max-width: $breakpoint-sm) {
      @content;
    }
  } @else if $breakpoint == md {
    @media (min-width: $breakpoint-sm) and (max-width: $breakpoint-md) {
      @content;
    }
  } @else if $breakpoint == lg {
    @media (min-width: $breakpoint-md) and (max-width: $breakpoint-lg) {
      @content;
    }
  } @else if $breakpoint == xl {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  }
}

// 安全区适配
@mixin safe-area-inset($position: bottom, $property: padding) {
  #{$property}-#{$position}: env(safe-area-inset-#{$position});
} 