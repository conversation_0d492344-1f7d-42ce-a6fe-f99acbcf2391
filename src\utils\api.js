import { get, post, postFormData } from "./axios";
import { showNotify } from "vant";
import { BASE_URL, BASE_IMG_URL } from "@/config/config";
import { previewBlobForMobile } from "@/utils/mobileDownload";

/**
 * 接口服务类
 * 封装所有API请求方法
 */
class ApiService {
  /**
   * 用户模块接口
   */
  user = {
    /**
     * 用户登录
     * @param {Object} data - 登录信息
     * @returns {Promise} - 返回登录结果
     */
    login: (data = {}) => {
      const url = "/wx/pda/login/users/sparts";
      return post(url, data);
    },

    /**
     * 修改密码
     * @param {Object} data - 密码数据
     * @returns {Promise} - 返回操作结果
     */
    modifyPassword: (data = {}) => {
      const url = "/a/sys/user/modifyPwd";
      return post(url, data, { "Content-Type": "application/json" }, false);
    },

    /**
     * 退出登录
     * @returns {Promise} - 返回操作结果
     */
    logout: () => {
      const url = "/a/logout2";
      // 修改请求方式，尝试使用表单格式而非JSON格式
      return post(url, {}, { "Content-Type": "application/x-www-form-urlencoded" }, true).catch(
        (error) => {
          // 即使请求失败，也返回一个成功的Promise，让用户可以正常登出
          return Promise.resolve();
        }
      );
    },

    /**
     * 获取用户个人资料
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回用户资料
     */
    getUserProfile: (data = {}) => {
      const url = "/a/sys/user/show/data";
      return post(url, data);
    },

    /**
     * 更新用户个人资料
     * @param {Object} data - 更新的资料数据
     * @returns {Promise} - 返回操作结果
     */
    updateUserProfile: (data = {}) => {
      const url = "/a/sys/user/updateProfile";
      return post(url, data);
    },

    /**
     * 获取用户信息
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回用户信息
     */
    getUserInfo: (data = {}) => {
      const url = "/a/sys/user/infoData";
      return post(url, data);
    },
  };

  /**
   * 目录模块接口
   */
  catalog = {
    /**
     * 获取品牌列表
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回品牌列表
     */
    getBrandList: (data = {}) => {
      const url = "/a/pmc/catalog/pmcMaterielBomCatalog/brand";
      return post(url, data);
    },

    /**
     * 根据类型获取产品目录
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回产品目录
     */
    getProductCatalog: (data = {}) => {
      const url = "/a/pmc/catalog/pmcMaterielBomCatalog/catalogByType";
      return post(url, data);
    },

    /**
     * 获取推荐零件
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回推荐零件列表
     */
    getRecommendedItems: (data = {}) => {
      const url = "a/pmc/catalog/pmcMaterielBomItemsRecommend/listData";
      return post(url, data);
    },

    /**
     * 零件搜索
     * @param {Object} data - 搜索参数
     * @returns {Promise} - 返回搜索结果
     */
    searchSparts: (data = {}) => {
      const url = "/a/pmc/catalog/pmcMaterielBomCatalog/spartPage";
      return post(url, data);
    },

    /**
     * 产品搜索
     * @param {Object} data - 搜索参数
     * @returns {Promise} - 返回搜索结果
     */
    searchProducts: (data = {}) => {
      const url = "/a/pmc/catalog/pmcMaterielBomCatalog/productPage";
      return post(url, data);
    },

    /**
     * 根据ID获取目录
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回目录信息
     */
    getCatalogById: (data = {}) => {
      const url = "/windows/pythonQT/view/getCatalogById";
      return post(url, data);
    },

    /**
     * 获取产品目录
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回产品目录
     */
    getCatalogProduct: (data = {}) => {
      const url = "/windows/pythonQT/view/product";
      return post(url, data);
    },

    /**
     * 获取配件表物料明细
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回物料明细
     */
    getTableData: (data = {}) => {
      const url = "a/pmc/catalog.item/pmcMaterielBomItems/listData";
      return post(url, data);
    },

    /**
     * 获取坐标信息
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回坐标信息
     */
    getCoordinate: (data = {}) => {
      const url = "a/pmc/catalog/pmcMaterielBomItemsJsonCoordinate/listData";
      return post(url, data);
    },

    /**
     * 保存坐标信息
     * @param {Object} data - 坐标数据
     * @returns {Promise} - 返回操作结果
     */
    saveCoordinate: (data = {}) => {
      const url = "/a/pmc/catalog/pmcMaterielBomItemsJsonCoordinate/save";
      return post(url, data, { "Content-Type": "application/json" }, false);
    },

    /**
     * 删除坐标信息
     * @param {Object} data - 删除参数
     * @returns {Promise} - 返回操作结果
     */
    deleteCoordinate: (data = {}) => {
      const url = "/a/pmc/catalog/pmcMaterielBomItemsJsonCoordinate/delete";
      return post(url, data);
    },

    /**
     * 获取BOM项使用情况
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回使用情况
     */
    getBomItemsUsedIn: (data = {}) => {
      const url = "a/pmc/catalog.item/pmcMaterielBomItems/usedIn";
      return post(url, data);
    },
  };

  /**
   * 购物车与收藏夹模块接口
   */
  cart = {
    /**
     * 加入购物车
     * @param {Object} data - 购物车数据
     * @returns {Promise} - 返回操作结果
     */
    addToCart: (data = {}) => {
      const url = "/a/pmc/shopCart/addToCart";
      return post(url, data, { "Content-Type": "application/json" }, false);
    },

    /**
     * 加入收藏夹
     * @param {Object} data - 收藏数据
     * @returns {Promise} - 返回操作结果
     */
    addToFavorites: (data = {}) => {
      const url = "/a/pmc/shopCart/addToFavorites";
      return post(url, data, { "Content-Type": "application/json" }, false);
    },

    /**
     * 查询当前用户购物车
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回购物车数据
     */
    getCurrentUserCart: (data = {}) => {
      const url = "/a/pmc/shopCart/currentUserShopCarListPage";
      return post(url, data);
    },

    /**
     * 查询当前用户收藏夹
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回收藏夹数据
     */
    getCurrentUserFavorites: (data = {}) => {
      const url = "/a/pmc/shopCart/currentUserFavoriteslistPage";
      return post(url, data);
    },

    /**
     * 更新购物车商品数量
     * @param {Object} data - 更新数据
     * @returns {Promise} - 返回操作结果
     */
    updateCartItemCount: (data = {}) => {
      const url = "/a/pmc/shopCart/updCartCount";
      return post(url, data, { "Content-Type": "application/json" }, false);
    },

    /**
     * 删除购物车商品
     * @param {Object} data - 删除数据
     * @returns {Promise} - 返回操作结果
     */
    deleteCartItem: (data = {}) => {
      const url = "/a/pmc/shopCart/delete";
      return post(url, data);
    },

    /**
     * 查询购物车和收藏夹
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回查询结果
     */
    listDataCurrentUserShopCar: (data = {}) => {
      const url = "/a/pmc/shopCart/listData_currentUser";
      return post(url, data);
    },
  };

  /**
   * 系统模块接口
   */
  system = {
    /**
     * 获取菜单数据
     * @returns {Promise} - 返回菜单数据
     */
    getMenu: () => {
      const url = "/windows/pythonQT/view/listData";
      return get(url, {});
    },
  };

  /**
   * 通知模块接口
   */
  notification = {
    /**
     * 获取未读通知数量
     * @returns {Promise} - 返回未读通知数量
     */
    getUnreadCount: () => {
      const url = "/a/pmc/notice/pmcNotice/noticeNumber";
      return post(url);
    },

    /**
     * 获取我的通知
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回通知列表
     */
    getMyNotice: (data = {}) => {
      const url = "/a/pmc/notice/pmcNotice/myNotice";
      return post(url, data);
    },

    /**
     * 阅读通知
     * @param {Object} data - 通知数据
     * @returns {Promise} - 返回操作结果
     */
    readNotice: (data = {}) => {
      const url = "/a/pmc/notice/pmcNotice/readNotice";
      return post(url, data);
    },

    /**
     * 获取通知列表
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回通知列表
     */
    getNoticeList: (data = {}) => {
      const url = "/a/pmc/notice/pmcNotice/listData";
      return post(url, data);
    },
  };

  /**
   * 订单模块接口
   */
  order = {
    /**
     * 保存购物车订单
     * @param {Object} data - 订单数据
     * @returns {Promise} - 返回操作结果
     */
    saveShopCarOrder: (data = {}) => {
      const url = "/a/pmc/shopCart/shopCarOrderSave";
      return post(url, data, { "Content-Type": "application/json" }, false);
    },

    /**
     * 获取订单列表
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回订单列表
     */
    getOrderList: (data = {}) => {
      const url = "/a/pmc/shopOrder/listPageHead";
      return post(url, data);
    },

    /**
     * 获取订单列表分页数据
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回订单列表分页数据
     */
    getOrderListPage: (data = {}) => {
      const url = "/a/pmc/shopOrder/listPageHead";
      return post(url, data);
    },

    /**
     * 查询订单信息
     * @param {Object} data - 订单数据
     * @returns {Promise} - 返回订单信息
     */
    queryOrderInfo: (data) => {
      const url = "/a/pmc/shopOrder/queryShopOrderInfo";
      return post(url, data);
    },

    /**
     * 获取订单详情
     * @param {string} orderId - 订单ID
     * @returns {Promise} - 返回订单详情
     */
    getOrderDetail: (orderId) => {
      const url = "/a/pmc/shopOrder/queryShopOrderInfo";
      return post(url, { orderId });
    },

    /**
     * 查看订单信息
     * @param {Object} data - 订单数据
     * @returns {Promise} - 返回订单信息
     */
    queryOrderInfoView: (data) => {
      const url = "/windows/pythonQT/view/queryShopOrderInfo";
      return post(url, data);
    },

    /**
     * 确认订单
     * @param {string} orderId - 订单ID
     * @returns {Promise} - 返回操作结果
     */
    confirmOrder: (orderId) => {
      const url = "/a/pmc/shopOrder/confirmOrder?orderId=" + orderId;
      return post(url, {}, { "Content-Type": "application/json" }, false);
    },

    /**
     * 删除订单
     * @param {string} orderId - 订单ID
     * @returns {Promise} - 返回操作结果
     */
    deleteOrder: (orderId) => {
      const url = "/a/pmc/shopOrder/deleteOrder_shopMall?orderId=" + orderId;
      return post(url, {}, { "Content-Type": "application/json" }, false);
    },

    /**
     * 生成发票PDF
     * @param {Object} data - 订单数据
     * @returns {Promise} - 返回操作结果
     */
    generateInvoicePdf: (data) => {
      const url = "/a/pmc/shopOrder/generatePdfInvoice";
      return post(url, data);
    },

    /**
     * 创建新订单
     * @param {Object} data - 订单数据
     * @returns {Promise} - 返回操作结果
     */
    createOrder: (data = {}) => {
      const url = "/a/pmc/shopOrder/save";
      return post(url, data, { "Content-Type": "application/json" }, false);
    },
  };

  /**
   * 客户管理模块接口
   */
  customer = {
    /**
     * 搜索用户联系人
     * @param {Object} data - 搜索参数
     * @returns {Promise} - 返回搜索结果
     */
    searchUserLinkMan: (data = {}) => {
      const url = "/a/pmc/basic/companyLinkman/searchUserLinkMan";
      return post(url, data);
    },

    /**
     * 保存用户联系人
     * @param {Object} data - 联系人数据
     * @returns {Promise} - 返回操作结果
     */
    saveUserLinkMan: (data = {}) => {
      const url = "/a/pmc/basic/companyLinkman/saveUserLinkMan";
      return post(url, data);
    },

    /**
     * 删除用户联系人
     * @param {Object} data - 删除参数
     * @returns {Promise} - 返回操作结果
     */
    deleteUserLinkMan: (data = {}) => {
      const url = "/a/pmc/basic/companyLinkman/remove";
      return post(url, data);
    },

    /**
     * 获取经销商账户列表
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回账户列表
     */
    getDealerAccountList: (data = {}) => {
      const url = "/a/sys/user/listDataMall?userType=2";
      return post(url, data);
    },

    /**
     * 获取经销商公司列表
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回公司列表
     */
    getDealerCompanyList: (data = {}) => {
      const url = "/a/pmc/basic/companyInfo/list/data?bizType=2";
      return post(url, data);
    },
  };

  /**
   * 产品注册模块接口
   */
  productRegister = {
    /**
     * 获取注册产品列表
     * @param {Object} data - 请求参数
     * @returns {Promise} - 返回产品列表
     */
    getRegisterList: (data = {}) => {
      const url = "/a/pmc/claims/pmcClaimsRegisterProduct/registerlistPage";
      return post(url, data);
    },

    /**
     * 保存注册产品
     * @param {Object} data - 产品数据
     * @returns {Promise} - 返回操作结果
     */
    saveRegisterProduct: (data = {}) => {
      const url = "/a/pmc/claims/pmcClaimsRegisterProduct/saveRegisterProduct";
      return postFormData(url, data);
    },

    /**
     * 确认注册产品
     * @param {Object} data - 产品数据
     * @returns {Promise} - 返回操作结果
     */
    confirmRegisterProduct: (data = {}) => {
      const url = "/a/pmc/claims/pmcClaimsRegisterProduct/confirmRegisterProduct";
      return post(url, data);
    },

    /**
     * 删除注册产品
     * @param {Object} data - 删除参数
     * @returns {Promise} - 返回操作结果
     */
    deleteRegisterProduct: (data = {}) => {
      const url = "/a/pmc/claims/pmcClaimsRegisterProduct/delete";
      return post(url, data);
    },

    /**
     * 保存索赔记录
     * @param {Object} data - 索赔数据
     * @returns {Promise} - 返回操作结果
     */
    saveClaimsRecord: (data = {}) => {
      const url = "/a/pmc/claims/pmcClaimsRecord/saveClaimsRecord";
      return postFormData(url, data);
    },

    /**
     * 删除索赔记录
     * @param {Object} data - 删除参数
     * @returns {Promise} - 返回操作结果
     */
    deleteClaimsRecord: (data = {}) => {
      const url = "/a/pmc/claims/pmcClaimsRecord/delete";
      return post(url, data);
    },
  };

  /**
   * 工具方法
   */
  utils = {
    /**
     * 格式化价格显示
     * @param {number|string} value - 价格值
     * @returns {string} - 格式化后的价格字符串
     */
    formatPrice: (value) => {
      if (!value) return "0.00";

      // 转换为数字并保留两位小数
      const num = parseFloat(value);
      if (isNaN(num)) return "0.00";

      return num.toFixed(2);
    },

    /**
     * 格式化库存数量
     * @param {number|string} value - 库存值
     * @returns {string} - 格式化后的库存
     */
    formatStock: (value) => {
      if (!value) return "0";

      // 确保value是数字
      const numValue = parseInt(value);
      if (isNaN(numValue)) return "0";

      return numValue.toString();
    },

    /**
     * 获取图片URL
     * @param {Array} fileList - 文件列表
     * @param {string} type - 获取类型，'all'表示获取所有
     * @returns {Array|string} - 图片URL或URL数组
     */
    getImageUrl: (fileList, type = "one") => {
      if (!fileList || fileList.length === 0) {
        return type === "all" ? [] : "";
      }

      if (type === "all") {
        return fileList.map((item) => BASE_IMG_URL + item.filePath);
      } else {
        return BASE_IMG_URL + fileList[0].filePath;
      }
    },

    /**
     * 从树中根据指定字段和值查找节点
     * @param {Array} tree - 树形结构数据
     * @param {string} field - 字段名
     * @param {any} value - 字段值
     * @returns {Object|null} - 找到的节点或null
     */
    findNodeByFieldTree: (tree, field, value) => {
      for (let i = 0; i < tree.length; i++) {
        // 判断当前节点是否匹配
        if (tree[i][field] === value) {
          return tree[i];
        }
        // 如果当前节点有子节点，则递归查找
        if (tree[i].children) {
          const found = this.findNodeByFieldTree(tree[i].children, field, value);
          if (found) {
            return found;
          }
        }
      }
      return null; // 如果未找到，返回null
    },

    /**
     * 从树中根据ID查找节点
     * @param {Array} tree - 树形结构数据
     * @param {string} id - 节点ID
     * @returns {Object|null} - 找到的节点或null
     */
    findNodeByIdTree: (tree, id) => {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === id) {
          return tree[i];
        }
        if (tree[i].children) {
          const found = this.findNodeByIdTree(tree[i].children, id);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },

    /**
     * 下载文件
     * @param {string} url - 下载URL
     */
    loadDown: (url) => {
      if (!url) return;

      // 创建隐藏的iframe用于下载
      const iframe = document.createElement("iframe");
      iframe.style.display = "none";
      iframe.src = url;
      document.body.appendChild(iframe);
      // 移除iframe
      setTimeout(() => {
        document.body.removeChild(iframe);
      }, 5000);
    },

    /**
     * 下载PDF文件
     * @param {string} url - 下载URL
     * @returns {Promise} - 返回下载结果
     */
    downloadPDF: (url) => {
      showNotify({ type: "loading", message: "下载中...", duration: 0 });

      return new Promise((resolve) => {
        const xhr = new XMLHttpRequest();
        xhr.open("GET", url, true);
        xhr.responseType = "blob";

        xhr.onload = function () {
          showNotify({ type: "clear" });

          if (this.status === 200) {
            const blob = this.response;
            const header = xhr.getResponseHeader("Content-disposition") || "";
            const fileName = decodeURIComponent(header.split("filename=")[1] || "document.pdf");

            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = fileName;
            document.body.appendChild(link);
            link.click();

            setTimeout(() => {
              document.body.removeChild(link);
              URL.revokeObjectURL(link.href);
            }, 100);

            resolve(true);
          } else {
            showNotify({ type: "danger", message: `下载失败 (${this.status})` });
            resolve(false);
          }
        };

        xhr.onerror = () => {
          showNotify({ type: "clear" });
          showNotify({ type: "danger", message: "网络错误" });
          resolve(false);
        };

        xhr.send();
      });
    },

    /**
     * 预览文件
     * @param {string} path - 文件路径
     */
    previewFile: (path) => {
      const url = `/api/sparts-wb/a/file/downFileByPath?filePath=${path}`;

      showNotify({ type: "loading", message: "加载中...", duration: 0 });

      fetch(url, {
        method: "GET",
        headers: {
          accept: "application/pdf",
          "Content-Disposition": "inline",
          "Content-Type": "application/pdf",
        },
      })
        .then((response) => response.blob())
        .then((blob) => {
          showNotify({ type: "clear" });
          // 使用移动端优化的预览函数
          const success = previewBlobForMobile(blob, (error) => {
            showNotify({ type: "danger", message: "预览被阻止，请允许弹出窗口" });
          });

          if (!success) {
            showNotify({ type: "danger", message: "预览失败" });
          }
        })
        .catch((error) => {
          showNotify({ type: "clear" });
          showNotify({ type: "danger", message: "预览失败" });
        });
    },
  };

  /**
   * 文件URL常量
   */
  urls = {
    // BOM项Excel导出
    bomItemsExcel: `${BASE_URL}/windows/pythonQT/view/bomItemsExcel?bomId=`,

    // 订单装箱单Excel
    orderPackingListExcel: `${BASE_URL}/a/pmc/shopOrder/orderExcelPackingList?logisticsNumber=`,

    // 订单详情Excel
    orderDetailExcel: `${BASE_URL}/a/pmc/shopOrder/orderExcelShop?orderId=`,

    // 发票PDF
    generateInvoicePdf: `${BASE_URL}/a/pmc/shopOrder/generatePdfInvoice?orderId=`,
  };
}

// 创建API服务实例
const apiService = new ApiService();

// 导出API服务实例
export default apiService;
