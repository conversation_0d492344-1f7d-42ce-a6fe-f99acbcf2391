<template>
  <div class="product-info">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading size="24px" vertical>
        {{ $t('product.loadingProduct') }}
      </van-loading>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <van-empty
        image="error"
        :description="error"
      >
        <van-button
          type="primary"
          size="small"
          @click="$emit('retry')"
        >
          {{ $t('common.retry') }}
        </van-button>
      </van-empty>
    </div>

    <!-- 产品内容 -->
    <div v-else class="product-content">
      <!-- 产品图片 -->
      <div class="product-images">
        <van-image
          v-if="productMainImage"
          :src="productMainImage"
          fit="contain"
          :show-loading="true"
          :show-error="true"
          @click="previewImages"
          class="product-image"
        >
          <template #loading>
            <van-loading type="spinner" size="20" />
          </template>
          <template #error>
            <van-icon name="photo-fail" size="32" />
          </template>
        </van-image>

        <!-- 默认图片 -->
        <div v-else class="default-image">
          <van-icon name="photo" size="48" color="#c8c9cc" />
          <div class="default-text">{{ $t('product.noImage') }}</div>
        </div>
      </div>

      <!-- 产品信息 -->
      <div class="product-details">
        <!-- 产品标题 -->
        <div class="product-header">
          <h1 class="product-title">
            {{ productTitle }}
          </h1>
          <div class="product-code">
            {{ $t('product.partNumber') }}: {{ productData.materielCode || $t('product.unknown') }}
          </div>
        </div>

        <!-- 产品参数图片 -->
        <div v-if="productParamsImage" class="product-params-section">
          <div class="params-title">{{ $t('product.parameterImage') }}</div>
          <div class="params-image-container">
            <van-image
              :src="productParamsImage"
              fit="contain"
              :show-loading="true"
              :show-error="true"
              @click="previewParamsImage"
              class="params-image"
            >
              <template #loading>
                <div class="image-loading">
                  <van-loading type="spinner" size="20" />
                  <div class="loading-text">{{ $t('product.loadingImage') }}</div>
                </div>
              </template>
              <template #error>
                <div class="image-error">
                  <van-icon name="photo-fail" size="32" />
                  <div class="error-text">{{ $t('product.imageLoadFailed') }}</div>
                </div>
              </template>
            </van-image>
          </div>
        </div>

        <!-- 产品价格 -->
        <div class="product-price">
          <span class="currency-symbol">{{ currencySymbol }}</span>
          <span class="price-value">{{ formatPrice(productData.price) }}</span>
        </div>

        <!-- 产品状态指示器 -->
        <div class="product-status" v-if="showStatusIndicators">
          <div class="status-item" v-if="productData.existsCart == '1'">
            <van-icon name="shopping-cart" color="#ff6b35" />
            <span class="status-text">{{ $t('product.inCart') }}</span>
          </div>
          <div class="status-item" v-if="productData.existsFavorites == '1'">
            <van-icon name="star" color="#ffd700" />
            <span class="status-text">{{ $t('product.inFavorites') }}</span>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="product-actions">
        <van-button
          :icon="productData.existsFavorites == '1' ? 'star' : 'star-o'"
          :type="productData.existsFavorites == '1' ? 'warning' : 'default'"
          size="large"
          @click="handleFavoriteAction"
          :loading="favoriteLoading"
          class="action-button favorite-button"
          :title="productData.existsFavorites == '1' ? $t('product.removeFromFavorites') : $t('product.addToFavorites')"
        />

        <van-button
          :icon="productData.existsCart == '1' ? 'shopping-cart' : 'shopping-cart-o'"
          :type="productData.existsCart == '1' ? 'warning' : 'primary'"
          size="large"
          @click="showQuantityPopup = true"
          :loading="cartLoading"
          class="action-button cart-button"
        >
          {{ productData.existsCart == '1' ? $t('product.removeFromCart') : $t('product.addToCart') }}
        </van-button>
      </div>
    </div>

    <!-- 数量选择弹窗 -->
    <van-popup v-model:show="showQuantityPopup" round closeable position="bottom">
      <div class="quantity-popup">
        <div class="popup-header">
          <div class="popup-title">{{ $t('product.selectQuantity') }}</div>
          <div class="product-info-mini">
            <span class="mini-title">{{ productTitle }}</span>
            <span class="mini-price">
              <span class="currency-symbol">{{ currencySymbol }}</span>{{ formatPrice(productData.price) }}
            </span>
          </div>
        </div>

        <div class="quantity-section">
          <div class="quantity-label">{{ $t('product.quantity') }}</div>
          <van-stepper
            v-model="selectedQuantity"
            :min="1"
            :max="999"
            integer
            button-size="36"
            input-width="80"
          />
        </div>

        <div class="popup-actions">
          <van-button
            @click="showQuantityPopup = false"
            size="large"
            class="cancel-button"
          >
            {{ $t('common.cancel') }}
          </van-button>
          <van-button
            type="primary"
            @click="handleCartAction"
            :loading="cartLoading"
            size="large"
            class="confirm-button"
          >
            {{ productData.existsCart == '1' ? $t('product.removeFromCart') : $t('product.addToCart') }}
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, watch, inject } from 'vue'
import { useI18n } from 'vue-i18n'
import { showImagePreview } from 'vant'

const { t } = useI18n()

// 注入全局变量
const base_downFileByPath_url = inject('$base_downFileByPath_url')

const props = defineProps({
  productData: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['add-to-cart', 'add-to-favorites', 'retry'])

// 响应式数据
const selectedQuantity = ref(1)
const cartLoading = ref(false)
const favoriteLoading = ref(false)
const showQuantityPopup = ref(false)

// 计算属性
// 产品标题 - 参考PC端 tab_list.product[0].tittle
const productTitle = computed(() => {
  return props.productData.tittle ||
         props.productData.materielNameEn ||
         props.productData.materielName ||
         t('product.unknownProduct')
})

// 产品主图片 - 参考PC端图片处理逻辑
const productMainImage = computed(() => {
  // 参考PC端：使用 base_downFileByPath_url + carousel_list?.[0]?.filePath 模式
  if (props.productData.carousel_list && props.productData.carousel_list.length > 0) {
    const firstImage = props.productData.carousel_list[0]
    if (firstImage && firstImage.filePath) {
      return base_downFileByPath_url + firstImage.filePath
    }
  }

  // 如果没有carousel_list，尝试从productFileList获取
  if (props.productData.productFileList && Array.isArray(props.productData.productFileList)) {
    // 过滤出产品图片文件，参考PC端逻辑
    const productFiles = props.productData.productFileList.filter(file => {
      const filePath = typeof file === 'string' ? file : (file && file.filePath ? file.filePath : '')
      return filePath.includes('/product/product') &&
             (filePath.endsWith('.jpg') || filePath.endsWith('.png') || filePath.endsWith('.jpeg'))
    })

    // 按文件路径排序，参考PC端逻辑
    const sortedFiles = productFiles.sort((a, b) => {
      const pathA = typeof a === 'string' ? a : a.filePath
      const pathB = typeof b === 'string' ? b : b.filePath
      return pathA.localeCompare(pathB)
    })

    if (sortedFiles.length > 0) {
      const firstFile = sortedFiles[0]
      const filePath = typeof firstFile === 'string' ? firstFile : firstFile.filePath
      return base_downFileByPath_url + filePath
    }
  }

  // 如果还是没有图片，尝试从其他字段获取
  if (props.productData.imagePath) {
    return base_downFileByPath_url + props.productData.imagePath
  }

  return null
})

// 所有产品图片列表（用于预览）
const productImages = computed(() => {
  const images = []

  // 从carousel_list获取
  if (props.productData.carousel_list && Array.isArray(props.productData.carousel_list)) {
    props.productData.carousel_list.forEach(item => {
      if (item && item.filePath) {
        images.push(base_downFileByPath_url + item.filePath)
      }
    })
  }

  // 如果carousel_list为空，从productFileList获取
  if (images.length === 0 && props.productData.productFileList && Array.isArray(props.productData.productFileList)) {
    const productFiles = props.productData.productFileList.filter(file => {
      const filePath = typeof file === 'string' ? file : (file && file.filePath ? file.filePath : '')
      return filePath.includes('/product/product') &&
             (filePath.endsWith('.jpg') || filePath.endsWith('.png') || filePath.endsWith('.jpeg'))
    })

    const sortedFiles = productFiles.sort((a, b) => {
      const pathA = typeof a === 'string' ? a : a.filePath
      const pathB = typeof b === 'string' ? b : b.filePath
      return pathA.localeCompare(pathB)
    })

    sortedFiles.forEach(file => {
      const filePath = typeof file === 'string' ? file : file.filePath
      images.push(base_downFileByPath_url + filePath)
    })
  }

  // 如果还是没有图片，添加主图片
  if (images.length === 0 && productMainImage.value) {
    images.push(productMainImage.value)
  }

  return images
})

// 产品参数图片 - 参考PC端product_params_img逻辑
const productParamsImage = computed(() => {
  // 优先从product_params_img获取
  if (props.productData.product_params_img && props.productData.product_params_img.filePath) {
    return base_downFileByPath_url + props.productData.product_params_img.filePath
  }

  // 如果没有product_params_img，从productFileList中查找包含"parameter"的文件
  if (props.productData.productFileList && Array.isArray(props.productData.productFileList)) {
    const parameterFile = props.productData.productFileList.find(file => {
      const filePath = typeof file === 'string' ? file : (file && file.filePath ? file.filePath : '')
      return filePath.includes('parameter') &&
             (filePath.endsWith('.jpg') || filePath.endsWith('.png') || filePath.endsWith('.jpeg'))
    })

    if (parameterFile) {
      const filePath = typeof parameterFile === 'string' ? parameterFile : parameterFile.filePath
      return base_downFileByPath_url + filePath
    }
  }

  return null
})

// 是否显示状态指示器
const showStatusIndicators = computed(() => {
  return props.productData.existsCart == '1' || props.productData.existsFavorites == '1'
})

// 动态货币符号 - 参考PC端逻辑
const currencySymbol = computed(() => {
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  const currency = userInfo?.currency || '2'
  // 参考PC端：userInfo.currency === '1' ? '¥' : userInfo.currency === '2' ? '$' : userInfo.currency === '3' ? '€' : ''
  switch (currency) {
    case '1': return '¥'
    case '2': return '$'
    case '3': return '€'
    default: return '$'
  }
})

// 方法定义
// 价格格式化 - 参考PC端formatPrice方法
const formatPrice = (price) => {
  if (!price || isNaN(price)) return '0.00'
  return parseFloat(price).toFixed(2)
}

// 图片预览 - 支持多图预览
const previewImages = (startIndex = 0) => {
  if (productImages.value.length > 0) {
    showImagePreview({
      images: productImages.value,
      startPosition: startIndex,
      closeable: true
    })
  }
}

// 参数图片预览 - 单独预览参数图片
const previewParamsImage = () => {
  if (productParamsImage.value) {
    showImagePreview({
      images: [productParamsImage.value],
      startPosition: 0,
      closeable: true
    })
  }
}

// 购物车操作 - 参考PC端addWholeCart方法
const handleCartAction = async () => {
  cartLoading.value = true
  try {
    // 参考PC端：使用buyNumber.value作为数量，bomItemsId为产品ID，productType为"product"
    const productWithQuantity = {
      ...props.productData,
      amount: selectedQuantity.value,
      bomItemsId: props.productData.id,
      productType: 'product'
    }
    emit('add-to-cart', productWithQuantity)
    showQuantityPopup.value = false
  } finally {
    cartLoading.value = false
  }
}

// 收藏夹操作 - 参考PC端addWholeFavorit方法
const handleFavoriteAction = async () => {
  favoriteLoading.value = true
  try {
    // 参考PC端：bomItemsId为产品ID，productType为"product"
    const favoriteData = {
      ...props.productData,
      bomItemsId: props.productData.id,
      productType: 'product'
    }
    emit('add-to-favorites', favoriteData)
  } finally {
    favoriteLoading.value = false
  }
}

// 监听产品数据变化，重置数量和关闭弹窗
watch(() => props.productData, () => {
  selectedQuantity.value = 1
  showQuantityPopup.value = false
}, { deep: true })
</script>

<style scoped>
.product-info {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.loading-container,
.error-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
}

.product-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.product-images {
  height: 230px;
  background: #f8f9fa;
  position: relative;
  /* 确保图片区域能够正常滚动，不固定在屏幕上 */
}

.product-image {
  width: 100%;
  height: 100%;
  background: white;
  cursor: pointer;
  transition: opacity 0.2s;
}

.product-image:active {
  opacity: 0.8;
}

.default-image {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.default-text {
  margin-top: 8px;
  font-size: 14px;
  color: #c8c9cc;
}

.product-details {
  flex: 1;
  padding: 20px 16px;
}

.product-header {
  margin-bottom: 24px;
}

.product-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.4;
  word-break: break-word;
}

.product-code {
  font-size: 14px;
  color: #666;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  display: inline-block;
}

/* 产品参数图片样式 */
.product-params-section {
  margin: 24px 0;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.params-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.params-title::before {
  content: '';
  width: 3px;
  height: 16px;
  background: #ff6b35;
  margin-right: 8px;
  border-radius: 2px;
}

.params-image-container {
  height: 330px; /* 增大参数图片尺寸：从220px增加到300px */
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.params-image-container:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.params-image {
  width: 100%;
  height: 100%;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.params-image:active {
  transform: scale(0.98);
}

.image-loading,
.image-error {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
}

.loading-text,
.error-text {
  margin-top: 8px;
  font-size: 14px;
}

.image-error {
  color: #ff6b35;
}

.product-price {
  padding: 20px 0;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: baseline;
}

.currency-symbol {
  font-size: 24px;
  color: #ff6b35;
  font-weight: 600;
  margin-right: 4px;
}

.price-value {
  font-size: 32px;
  color: #ff6b35;
  font-weight: 700;
}

.product-status {
  margin-top: 16px;
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f0f9ff;
  border-radius: 16px;
  font-size: 12px;
}

.status-text {
  color: #666;
  font-weight: 500;
}

.product-actions {
  padding: 16px;
  background: white;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  align-items: center;
  /* 修改定位方式，避免遮挡底部导航栏 */
  margin-bottom: 60px; /* 为底部导航栏预留空间 */
}

.favorite-button {
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  border-radius: 8px;
}

.cart-button {
  flex: 1;
  height: 48px;
  font-weight: 600;
  border-radius: 8px;
  font-size: 16px;
}

.action-button {
  -webkit-tap-highlight-color: transparent;
  transition: transform 0.1s ease;
}

.action-button:active {
  transform: scale(0.98);
}

/* 数量选择弹窗样式 */
.quantity-popup {
  padding: 24px;
  min-height: 240px;
}

.popup-header {
  margin-bottom: 24px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.product-info-mini {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}

.mini-title {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  flex: 1;
  margin-right: 12px;
  word-break: break-word;
}

.mini-price {
  font-size: 16px;
  color: #ff6b35;
  font-weight: 600;
  white-space: nowrap;
}

.mini-price .currency-symbol {
  font-size: 14px;
  margin-right: 2px;
}

.quantity-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.quantity-label {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.popup-actions {
  display: flex;
  gap: 12px;
}

.cancel-button {
  flex: 1;
  height: 48px;
  border: 1px solid #ddd;
  color: #666;
  border-radius: 8px;
  font-size: 16px;
}

.confirm-button {
  flex: 2;
  height: 48px;
  font-weight: 600;
  border-radius: 8px;
  font-size: 16px;
}

/* 响应式设计 - 移动端优先 */

/* 小屏手机适配 (宽度 ≤ 375px) */
@media (max-width: 375px) {
  .product-images {
    height: 200px;
  }

  .product-title {
    font-size: 18px;
    line-height: 1.3;
  }

  .product-code {
    font-size: 13px;
    padding: 6px 10px;
  }

  .price-value {
    font-size: 28px;
  }

  .currency-symbol {
    font-size: 20px;
  }

  .product-details {
    padding: 16px 12px;
  }

  /* 小屏幕下参数图片调整 */
  .product-params-section {
    margin: 20px 0;
    padding: 12px 0;
  }

  .params-title {
    font-size: 15px;
    margin-bottom: 10px;
  }

  .params-image-container {
    height: 250px; /* 小屏幕下增大参数图片：从180px增加到250px */
    border-radius: 8px;
  }

  .loading-text,
  .error-text {
    font-size: 13px;
  }

  .product-actions {
    padding: 12px;
    gap: 8px;
    margin-bottom: 55px; /* 小屏幕下为导航栏预留空间 */
  }

  .cart-button {
    height: 44px;
    font-size: 15px;
  }

  .favorite-button {
    width: 44px;
    height: 44px;
  }

  .quantity-popup {
    padding: 20px;
  }

  .popup-title {
    font-size: 16px;
  }

  .cancel-button,
  .confirm-button {
    height: 44px;
    font-size: 15px;
  }

  .status-item {
    padding: 4px 8px;
    font-size: 11px;
  }
}

/* 基础横屏模式适配 (所有横屏设备) */
@media (orientation: landscape) {
  .product-images {
    height: 180px;
  }

  .product-details {
    padding: 12px 16px;
  }

  .product-header {
    margin-bottom: 16px;
  }

  .product-title {
    font-size: 18px;
    margin-bottom: 8px;
  }

  .product-code {
    font-size: 13px;
    padding: 6px 10px;
  }

  .price-value {
    font-size: 28px;
  }

  .currency-symbol {
    font-size: 22px;
  }

  /* 横屏模式下参数图片调整 */
  .product-params-section {
    margin: 12px 0;
    padding: 12px 0;
  }

  .params-title {
    font-size: 15px;
    margin-bottom: 8px;
  }

  .params-image-container {
    height: 180px; /* 横屏模式下优化参数图片尺寸 */
    border-radius: 8px;
  }

  .product-actions {
    position: relative;
    border-top: 1px solid #eee;
    background: white;
    padding: 8px 16px;
    margin-bottom: 50px; /* 横屏模式下也为导航栏预留空间 */
  }

  .cart-button {
    height: 40px;
    font-size: 14px;
  }

  .favorite-button {
    width: 40px;
    height: 40px;
  }

  .status-item {
    padding: 4px 10px;
    font-size: 11px;
  }
}

/* 大屏手机横屏模式 (宽度 > 567px) */
@media (orientation: landscape) and (min-width: 567px) {
  .product-images {
    height: 160px;
  }

  .product-details {
    padding: 10px 16px;
  }

  .product-header {
    margin-bottom: 12px;
  }

  .product-title {
    font-size: 16px;
    margin-bottom: 6px;
  }

  .product-code {
    font-size: 12px;
    padding: 4px 8px;
  }

  .price-value {
    font-size: 24px;
  }

  .currency-symbol {
    font-size: 18px;
  }

  /* 大屏横屏下参数图片进一步优化 */
  .product-params-section {
    margin: 10px 0;
    padding: 10px 0;
  }

  .params-title {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .params-image-container {
    height: 150px; /* 大屏横屏下紧凑的参数图片尺寸 */
    border-radius: 6px;
  }

  .product-actions {
    padding: 6px 16px;
    margin-bottom: 45px;
  }

  .cart-button {
    height: 36px;
    font-size: 13px;
  }

  .favorite-button {
    width: 36px;
    height: 36px;
  }

  .status-item {
    padding: 3px 8px;
    font-size: 10px;
  }

  .quantity-popup {
    padding: 16px;
  }

  .popup-title {
    font-size: 15px;
  }

  .cancel-button,
  .confirm-button {
    height: 40px;
    font-size: 14px;
  }
}

/* 平板设备适配 (宽度 768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .product-images {
    height: 280px;
  }

  .product-details {
    padding: 24px 20px;
  }

  .product-header {
    margin-bottom: 28px;
  }

  .product-title {
    font-size: 22px;
    margin-bottom: 16px;
  }

  .product-code {
    font-size: 15px;
    padding: 10px 16px;
  }

  .price-value {
    font-size: 36px;
  }

  .currency-symbol {
    font-size: 28px;
  }

  /* 平板设备下参数图片优化 */
  .product-params-section {
    margin: 28px 0;
    padding: 20px 0;
  }

  .params-title {
    font-size: 18px;
    margin-bottom: 16px;
  }

  .params-image-container {
    height: 400px; /* 平板设备下大尺寸参数图片 */
    border-radius: 16px;
  }

  .product-actions {
    padding: 20px 24px;
    margin-bottom: 60px;
    gap: 16px;
  }

  .cart-button {
    height: 52px;
    font-size: 17px;
    border-radius: 12px;
  }

  .favorite-button {
    width: 52px;
    height: 52px;
    border-radius: 12px;
  }

  .status-item {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 20px;
  }

  .quantity-popup {
    padding: 32px;
    min-height: 280px;
  }

  .popup-title {
    font-size: 20px;
    margin-bottom: 16px;
  }

  .quantity-section {
    padding: 24px 0;
  }

  .quantity-label {
    font-size: 17px;
  }

  .cancel-button,
  .confirm-button {
    height: 52px;
    font-size: 17px;
    border-radius: 12px;
  }
}

/* 触摸优化和交互反馈 */
.product-image,
.params-image {
  -webkit-tap-highlight-color: transparent;
  transition: all 0.2s ease;
}

.product-image:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.params-image:active {
  transform: scale(0.98);
}

.action-button {
  -webkit-tap-highlight-color: transparent;
  transition: all 0.2s ease;
}

.action-button:active {
  transform: scale(0.98);
}

/* 确保所有交互元素满足最小触摸目标 */
.favorite-button {
  min-width: 44px;
  min-height: 44px;
}

.cart-button {
  min-height: 44px;
}

.cancel-button,
.confirm-button {
  min-height: 44px;
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .product-price {
    border-bottom: 0.5px solid #eee;
  }

  .product-params-section {
    border-top: 0.5px solid #f0f0f0;
    border-bottom: 0.5px solid #f0f0f0;
  }

  .product-actions {
    border-top: 0.5px solid #eee;
  }

  .quantity-section {
    border-top: 0.5px solid #f0f0f0;
    border-bottom: 0.5px solid #f0f0f0;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .product-info {
    background: #1a1a1a;
    color: #fff;
  }

  .product-details {
    background: #1a1a1a;
  }

  .product-title {
    color: #fff;
  }

  .product-code {
    background: #2a2a2a;
    color: #ccc;
  }

  /* 深色模式下参数图片样式 */
  .product-params-section {
    border-color: #333;
  }

  .params-title {
    color: #fff;
  }

  .params-image-container {
    background: #2a2a2a;
  }

  .image-loading,
  .image-error {
    color: #ccc;
  }

  .image-error {
    color: #ff8a65;
  }

  .product-actions {
    background: #1a1a1a;
    border-top-color: #333;
  }

  .quantity-popup {
    background: #1a1a1a;
    color: #fff;
  }

  .product-info-mini {
    background: #2a2a2a;
  }

  .mini-title {
    color: #fff;
  }

  .quantity-section {
    border-color: #333;
  }

  .quantity-label {
    color: #fff;
  }
}
</style>
