/* 强制横竖屏旋转样式 */

/* 强制横屏模式 */
body.force-landscape {
  /* 检测当前是否为竖屏状态 */
  @media (orientation: portrait) {
    /* 竖屏状态下强制旋转到横屏 */
    transform: rotate(90deg);
    transform-origin: center center;
    
    /* 调整视口尺寸 */
    width: 100vh;
    height: 100vw;
    
    /* 确保内容居中 */
    position: fixed;
    top: 0;
    left: 0;
    
    /* 重新计算位置，使内容居中 */
    margin-left: calc((100vw - 100vh) / 2);
    margin-top: calc((100vh - 100vw) / 2);
    
    /* 防止滚动条问题 */
    overflow-x: hidden;
  }
  
  /* 横屏状态下保持正常 */
  @media (orientation: landscape) {
    transform: none;
    width: 100vw;
    height: 100vh;
    position: static;
    margin: 0;
  }
}

/* 强制竖屏模式 */
body.force-portrait {
  /* 检测当前是否为横屏状态 */
  @media (orientation: landscape) {
    /* 横屏状态下强制旋转到竖屏 */
    transform: rotate(-90deg);
    transform-origin: center center;
    
    /* 调整视口尺寸 */
    width: 100vh;
    height: 100vw;
    
    /* 确保内容居中 */
    position: fixed;
    top: 0;
    left: 0;
    
    /* 重新计算位置，使内容居中 */
    margin-left: calc((100vw - 100vh) / 2);
    margin-top: calc((100vh - 100vw) / 2);
    
    /* 防止滚动条问题 */
    overflow-x: hidden;
  }
  
  /* 竖屏状态下保持正常 */
  @media (orientation: portrait) {
    transform: none;
    width: 100vw;
    height: 100vh;
    position: static;
    margin: 0;
  }
}

/* 旋转动画效果 */
body.force-landscape,
body.force-portrait {
  transition: transform 0.3s ease-in-out, 
              width 0.3s ease-in-out, 
              height 0.3s ease-in-out,
              margin 0.3s ease-in-out;
}

/* 修复可能的布局问题 */
body.force-landscape *,
body.force-portrait * {
  box-sizing: border-box;
}

/* 确保全屏元素正确显示 */
body.force-landscape .van-popup--center,
body.force-portrait .van-popup--center {
  transform: none !important;
}

/* 修复drawer和popup在强制旋转时的显示问题 */
body.force-landscape .van-action-sheet,
body.force-portrait .van-action-sheet {
  transform: none !important;
}

/* 高级强制旋转方案 - 使用CSS Grid */
.force-rotation-container {
  display: grid;
  place-items: center;
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

.force-rotation-content {
  width: 100vh;
  height: 100vw;
  transform-origin: center center;
}

/* 横屏强制旋转 */
.force-rotation-container.landscape .force-rotation-content {
  transform: rotate(90deg);
}

/* 竖屏强制旋转 */
.force-rotation-container.portrait .force-rotation-content {
  transform: rotate(-90deg);
}

/* 移动设备优化 */
@media screen and (max-width: 768px) {
  body.force-landscape,
  body.force-portrait {
    /* 移动设备上的特殊处理 */
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    
    /* 防止iOS Safari的问题 */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}


