<template>
  <div class="order-detail-page">
    <van-loading v-if="loading" vertical size="24px" class="loading-spinner"
      >{{ t('order.loading') }}</van-loading
    >

    <template v-else>
      <!-- 订单状态卡片 -->
      <div class="status-card">
        <div class="status-icon">
          <van-icon name="checked" v-if="orderDetail.orderStatus === '1001'" />
          <van-icon
            name="clock-o"
            v-else-if="orderDetail.orderStatus === '1'"
          />
          <van-icon
            name="logistics"
            v-else-if="orderDetail.orderStatus === '20'"
          />
          <van-icon
            name="logistics"
            v-else-if="orderDetail.orderStatus === '30'"
          />
          <van-icon
            name="logistics"
            v-else-if="orderDetail.orderStatus === '1000'"
          />
          <van-icon name="question-o" v-else />
        </div>
        <div class="status-info">
          <div class="status-title">
            <van-tag type="warning" v-if="orderDetail.orderStatus === '1'"
              >{{ t('order.status.pending') }}</van-tag
            >
            <van-tag type="warning" v-if="orderDetail.orderStatus === '20'"
              >{{ t('order.status.unshipped') }}</van-tag
            >
            <van-tag type="danger" v-if="orderDetail.orderStatus === '30'"
              >{{ t('order.status.waitingPickup') }}</van-tag
            >
            <van-tag type="primary" v-if="orderDetail.orderStatus === '1000'"
              >{{ t('order.status.partialShipped') }}</van-tag
            >
            <van-tag type="success" v-if="orderDetail.orderStatus === '1001'"
              >{{ t('order.status.shipped') }}</van-tag
            >
          </div>
          <div class="status-desc">
            <span v-if="orderDetail.orderStatus === '1'">{{ t('order.statusDesc.pending') }}</span>
            <span v-else-if="orderDetail.orderStatus === '20'"
              >{{ t('order.statusDesc.unshipped') }}</span
            >
            <span v-else-if="orderDetail.orderStatus === '30'">{{ t('order.statusDesc.waitingPickup') }}</span>
            <span v-else-if="orderDetail.orderStatus === '1000'"
              >{{ t('order.statusDesc.partialShipped') }}</span
            >
            <span v-else-if="orderDetail.orderStatus === '1001'">
              {{ t('order.statusDesc.shipped', { time: estimateDeliveryTime }) }}
            </span>
          </div>
          <div
            class="status-date"
            v-if="
              orderDetail.deliveryTime &&
              (orderDetail.orderStatus === '1000' ||
                orderDetail.orderStatus === '1001')
            "
          >
            {{ t('order.shippingTime') }}：{{ formatDate(orderDetail.deliveryTime) }}
          </div>
        </div>
      </div>

      <!-- 订单基本信息 -->
      <van-cell-group inset class="info-group">
        <van-cell :title="t('order.orderNumber')" :value="orderDetail.id" />
        <van-cell
          :title="t('order.createDate')"
          :value="formatDate(orderDetail.createDate)"
        />
        <van-cell
          :title="t('order.shippingTime')"
          :value="formatDate(orderDetail.deliveryTime)"
        />
        <van-cell :title="t('orderCreate.paymentDate')" :value="formatDate(orderDetail.payment)" />
      </van-cell-group>

      <!-- 联系人信息 -->
      <van-cell-group inset :title="t('order.contactInfo')" class="info-group">
        <van-cell :title="t('order.contact')" :value="orderDetail.contactName" />
        <van-cell :title="t('order.contactPhone')" :value="orderDetail.contactPhone" />
        <van-cell :title="t('order.contactEmail')" :value="orderDetail.contactEmail" />
      </van-cell-group>

      <!-- 地址信息 -->
      <van-cell-group inset :title="t('order.addressInfo')" class="info-group">
        <van-cell :title="t('order.shippingAddress')">
          <template #value>
            <div class="address-text">{{ orderDetail.collectAddress }}</div>
          </template>
        </van-cell>
        <van-cell :title="t('order.billingAddress')">
          <template #value>
            <div class="address-text">{{ orderDetail.billingAddress }}</div>
          </template>
        </van-cell>
      </van-cell-group>


      <!-- 物流信息 -->
      <van-cell-group
        inset
        :title="t('order.logistics.info')"
        class="info-group"
        v-if="
          orderDetail.outWarehouseList &&
          orderDetail.outWarehouseList.length > 0
        "
      >
        <div
          v-for="(item, index) in orderDetail.outWarehouseList"
          :key="index"
          class="logistics-item"
        >
          <van-cell :title="`${t('order.logistics.info')} #${index + 1}`">
            <template #label>
              <div class="logistics-info">
                <div class="logistics-company">
                  {{ t('order.logistics.company') }}: {{ item.logisticsCompany }}
                </div>
                <div class="logistics-number">
                  <span>{{ t('order.logistics.number') }}: {{ item.logisticsNumber }}</span>
                </div>
                <div class="logistics-actions">
                  <van-button
                    type="primary"
                    size="small"
                    plain
                    icon="down"
                    @click="downloadExcel_packingList(item, orderDetail)"
                    >{{ t('order.logistics.packingList') }}</van-button
                  >
                </div>
              </div>
            </template>
          </van-cell>
        </div>
      </van-cell-group>

      <!-- 备注信息 -->
      <van-cell-group
        inset
        :title="t('order.remark')"
        class="info-group"
        v-if="orderDetail.remarks"
      >
        <van-cell>
          <template #value>
            <div class="remark-text">{{ orderDetail.remarks }}</div>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 订单商品列表 -->
      <div class="product-list-section">
        <div class="section-header">
          <div class="section-title">{{ t('order.orderItems') }}</div>
        </div>

        <!-- 商品列表 - 卡片式布局 -->
        <div class="product-cards">
          <!-- 使用orderDetailList字段 -->
          <div
            v-for="(item, index) in orderDetail.orderDetailList || []"
            :key="'detail-' + index"
            class="product-card"
            :class="getItemTypeClass(item)"
          >
            <div class="product-card-content">
              <div
                class="product-image"
                @click="previewImage(getImageUrl(item))"
              >
                <van-image :src="getImageUrl(item)[0]" fit="cover" lazy-load>
                  <template #error>
                    <div class="image-placeholder">
                      <van-icon name="photo-o" />
                    </div>
                  </template>
                </van-image>
              </div>
              <div class="product-info">
                <div class="product-header">
                  <span class="product-index">{{ index + 1 }}</span>
                  <span class="product-type-tag" :class="getItemType(item) + '-tag'">
                    {{ getItemTypeLabel(item) }}
                  </span>
                  <span class="product-code">{{ item.materielCode }}</span>
                </div>
                <div class="product-name">
                  {{ item.materielNameEn || item.materielName }}
                </div>
                <div class="product-price-info">
                  <div class="price-item">
                    <span class="price-label">{{ t('orderCreate.unitPrice') }}:</span>
                    <span class="price-value">
                      {{
                        userInfo.currency === "1"
                          ? "¥"
                          : userInfo.currency === "2"
                          ? "$"
                          : userInfo.currency === "3"
                          ? "€"
                          : ""
                      }}
                      {{ formatPrice(item.unitPrice || item.price) }}
                    </span>
                  </div>
                  <div class="price-item">
                    <span class="price-label">{{ t('orderCreate.quantity') }}:</span>
                    <span class="price-value">{{
                      item.amount || item.quantity || 1
                    }}</span>
                  </div>
                  <div class="price-item total-price">
                    <span class="price-label">{{ t('orderCreate.subtotal') }}:</span>
                    <span class="price-value">
                      {{
                        userInfo.currency === "1"
                          ? "¥"
                          : userInfo.currency === "2"
                          ? "$"
                          : userInfo.currency === "3"
                          ? "€"
                          : ""
                      }}
                      {{
                        formatPrice(
                          (item.unitPrice || item.price) *
                            (item.amount || item.quantity || 1)
                        )
                      }}
                    </span>
                  </div>
                </div>
                <div class="product-actions" v-if="item.remarks">
                  <van-button
                    type="primary"
                    size="mini"
                    plain
                    @click="showRemark(item.remarks)"
                    >{{ t('order.viewRemark') }}</van-button
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- 使用salesOrderItemList字段 -->
          <template
            v-if="
              !orderDetail.orderDetailList ||
              orderDetail.orderDetailList.length === 0
            "
          >
            <div
              v-for="(item, index) in orderDetail.salesOrderItemList || []"
              :key="'sales-' + index"
              class="product-card"
              :class="getItemTypeClass(item)"
            >
              <div class="product-card-content">
                <div
                  class="product-image"
                  @click="previewImage(getImageUrl(item))"
                >
                  <van-image :src="getImageUrl(item)[0]" fit="cover" lazy-load>
                    <template #error>
                      <div class="image-placeholder">
                        <van-icon name="photo-o" />
                      </div>
                    </template>
                  </van-image>
                </div>
                <div class="product-info">
                  <div class="product-header">
                    <span class="product-index">{{ index + 1 }}</span>
                    <span class="product-type-tag" :class="getItemType(item) + '-tag'">
                      {{ getItemTypeLabel(item) }}
                    </span>
                    <span class="product-code">{{ item.materielCode }}</span>
                  </div>
                  <div class="product-name">
                    {{ item.materielNameEn || item.materielName }}
                  </div>
                  <div class="product-price-info">
                    <div class="price-item">
                      <span class="price-label">{{ t('orderCreate.unitPrice') }}:</span>
                      <span class="price-value">
                        {{
                          userInfo.currency === "1"
                            ? "¥"
                            : userInfo.currency === "2"
                            ? "$"
                            : userInfo.currency === "3"
                            ? "€"
                            : ""
                        }}
                        {{ formatPrice(item.unitPrice || item.price) }}
                      </span>
                    </div>
                    <div class="price-item">
                      <span class="price-label">{{ t('orderCreate.quantity') }}:</span>
                      <span class="price-value">{{
                        item.amount || item.quantity || 1
                      }}</span>
                    </div>
                    <div class="price-item total-price">
                      <span class="price-label">{{ t('orderCreate.subtotal') }}:</span>
                      <span class="price-value">
                        {{
                          userInfo.currency === "1"
                            ? "¥"
                            : userInfo.currency === "2"
                            ? "$"
                            : userInfo.currency === "3"
                            ? "€"
                            : ""
                        }}
                        {{
                          formatPrice(
                            (item.unitPrice || item.price) *
                              (item.amount || item.quantity || 1)
                          )
                        }}
                      </span>
                    </div>
                  </div>
                  <div class="product-actions" v-if="item.remarks">
                    <van-button
                      type="primary"
                      size="mini"
                      plain
                      @click="showRemark(item.remarks)"
                      >{{ t('order.viewRemark') }}</van-button
                    >
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 使用productList字段，可能包含嵌套的cartList -->
          <template
            v-if="
              (!orderDetail.orderDetailList ||
                orderDetail.orderDetailList.length === 0) &&
              (!orderDetail.salesOrderItemList ||
                orderDetail.salesOrderItemList.length === 0) &&
              orderDetail.productList &&
              orderDetail.productList.length > 0
            "
          >
            <template
              v-for="(product, pIndex) in orderDetail.productList"
              :key="'product-' + pIndex"
            >
                              <div class="product-category">
                <div class="product-category-header">
                  <div 
                    class="product-category-image" 
                    @click="previewImage(getImageUrl(null, product))"
                  >
                    <van-image 
                      :src="getImageUrl(null, product).at(-1)" 
                      fit="cover"
                    >
                      <template #error>
                        <div class="image-placeholder">
                          <van-icon name="photo-o" />
                        </div>
                      </template>
                    </van-image>
                  </div>
                  <div class="product-category-title">
                    {{ product.materielCode }}
                    {{ product.materielNameEn || product.materielName }}
                  </div>
                </div>

                <div
                  v-for="(item, index) in product.cartList ||
                  product.salesOrderItemList ||
                  []"
                  :key="'product-item-' + pIndex + '-' + index"
                  class="product-card"
                  :class="getItemTypeClass(item)"
                >
                  <div class="product-card-content">
                    <div class="product-info">
                      <div class="product-header">
                        <span class="product-index"
                          >{{ pIndex + 1 }}.{{ index + 1 }}</span
                        >
                        <span class="product-type-tag" :class="getItemType(item) + '-tag'">
                          {{ getItemTypeLabel(item) }}
                        </span>
                        <span class="product-code">{{
                          item.materielCode
                        }}</span>
                      </div>
                      <div class="product-name">
                        {{ item.materielNameEn || item.materielName }}
                      </div>
                      <div class="product-price-info">
                        <div class="price-item">
                          <span class="price-label">{{ t('orderCreate.unitPrice') }}:</span>
                          <span class="price-value">
                            {{
                              userInfo.currency === "1"
                                ? "¥"
                                : userInfo.currency === "2"
                                ? "$"
                                : userInfo.currency === "3"
                                ? "€"
                                : ""
                            }}
                            {{ formatPrice(item.unitPrice || item.price) }}
                          </span>
                        </div>
                        <div class="price-item">
                          <span class="price-label">{{ t('orderCreate.quantity') }}:</span>
                          <span class="price-value">{{
                            item.amount || item.quantity || 1
                          }}</span>
                        </div>
                        <div class="price-item total-price">
                          <span class="price-label">{{ t('orderCreate.subtotal') }}:</span>
                          <span class="price-value">
                            {{
                              userInfo.currency === "1"
                                ? "¥"
                                : userInfo.currency === "2"
                                ? "$"
                                : userInfo.currency === "3"
                                ? "€"
                                : ""
                            }}
                            {{
                              formatPrice(
                                (item.unitPrice || item.price) *
                                  (item.amount || item.quantity || 1)
                              )
                            }}
                          </span>
                        </div>
                      </div>
                      <div class="product-actions" v-if="item.remarks">
                        <van-button
                          type="primary"
                          size="mini"
                          plain
                          @click="showRemark(item.remarks)"
                          >{{ t('order.viewRemark') }}</van-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </template>

          <!-- 没有商品时显示提示 -->
          <div
            v-if="
              (!orderDetail.orderDetailList ||
                orderDetail.orderDetailList.length === 0) &&
              (!orderDetail.salesOrderItemList ||
                orderDetail.salesOrderItemList.length === 0) &&
              (!orderDetail.productList || orderDetail.productList.length === 0)
            "
            class="empty-products"
          >
            <van-empty :description="t('order.emptyStates.noProducts')" />
          </div>
        </div>
      </div>

      <!-- 订单操作按钮 -->
      <div class="action-buttons">
        <van-button
          v-if="orderDetail?.orderStatus === '1'"
          type="primary"
          block
          icon="passed"
          @click="confirmOrder"
          >{{ t('order.confirmOrder') }}</van-button
        >

        <van-button
          v-if="isWithin30Min(orderDetail.createDate)"
          type="danger"
          block
          icon="delete"
          @click="showDeleteDialog"
          >{{ t('order.deleteOrder') }}</van-button
        >

        <div class="button-row">
          <van-button
            v-if="
              orderDetail.orderStatus == '1001' ||
              orderDetail.orderStatus == '1000'
            "
            type="warning"
            icon="bill"
            @click="downloadPdf(orderDetail)"
            >{{ t('order.messages.downloadInvoice') }}</van-button
          >

          <van-button icon="description" @click="downloadExcel(orderDetail)"
            >{{ t('order.export') }}</van-button
          >
        </div>
      </div>
    </template>

    <!-- 确认对话框 -->
    <van-dialog
      v-model:show="confirmDialogVisible"
      :title="t('order.confirmOrder')"
      show-cancel-button
      @confirm="confirmOrderAction"
    >
      {{ t('order.confirmOrderMessage') }}
    </van-dialog>

    <!-- 删除对话框 -->
    <van-dialog
      v-model:show="deleteDialogVisible"
      :title="t('order.deleteOrder')"
      show-cancel-button
      @confirm="deleteOrderAction"
    >
      {{ t('order.deleteOrderMessage') }}
    </van-dialog>

    <!-- 备注弹窗 -->
    <van-dialog
      v-model:show="remarkDialogVisible"
      :title="t('order.remark')"
      show-confirm-button
      :confirm-button-text="t('common.close')"
    >
      <div class="remark-dialog-content">
        {{ currentRemark }}
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  inject,
  defineProps,
  defineEmits,
  watch,
  computed,
} from "vue";
import { useI18n } from "vue-i18n";
import { showToast, showSuccessToast, showImagePreview } from "vant";
import dayjs from "dayjs";
import apiService from "@/utils/api";

const props = defineProps({
  params: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(["close"]);

// 基础配置
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const { t } = useI18n();
const userInfo = ref(JSON.parse(localStorage.getItem("userInfo") || "{}"));

// 状态变量
const loading = ref(true);
const orderDetail = ref({});
const confirmDialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const remarkDialogVisible = ref(false);
const currentRemark = ref("");

// 获取订单详情
const getOrderDetail = async () => {
  if (!props.params.id) return;

  loading.value = true;
  try {
    const result = await apiService.order.getOrderDetail(props.params.id);
    orderDetail.value = result.data;
  } catch (error) {
    console.error("获取订单详情失败:", error);
    showToast(t('order.operationFailed'));
  } finally {
    loading.value = false;
  }
};

// 日期格式化
const formatDate = (date) => {
  if (!date) return "";
  return dayjs(date).format("YYYY-MM-DD");
};

// 预计送达时间
const estimateDeliveryTime = computed(() => {
  if (!orderDetail.value.deliveryTime) return t('common.notSpecified');
  // 从发货日期算起，预计3-5天送达
  const deliveryDate = dayjs(orderDetail.value.deliveryTime);
  const estimatedMinDate = deliveryDate.add(3, "day").format("MM.DD");
  const estimatedMaxDate = deliveryDate.add(5, "day").format("MM.DD");
  return `${estimatedMinDate}-${estimatedMaxDate}`;
});

// 格式化价格显示
const formatPrice = (value) => {
  return apiService.utils.formatPrice(value);
};

// 检查是否在30分钟内
const isWithin30Min = (dateString) => {
  if (!dateString) return false;
  const currentDate = new Date();
  const givenDate = new Date(dateString);
  const timeDifference = currentDate.getTime() - givenDate.getTime();
  return timeDifference <= 0.5 * 60 * 60 * 1000;
};

// 显示确认对话框
const confirmOrder = () => {
  confirmDialogVisible.value = true;
};

// 显示删除对话框
const showDeleteDialog = () => {
  deleteDialogVisible.value = true;
};

// 显示备注内容
const showRemark = (remark) => {
  currentRemark.value = remark;
  remarkDialogVisible.value = true;
};

// 确认订单
const confirmOrderAction = async () => {
  try {
    await apiService.order.confirmOrder(orderDetail.value.id);
    showSuccessToast(t('order.messages.confirmOrderSuccess'));
    getOrderDetail();
  } catch (error) {
    showToast(t('order.messages.confirmOrderFailed'));
  }
};

// 删除订单
const deleteOrderAction = async () => {
  try {
    await apiService.order.deleteOrder(orderDetail.value.id);
    showSuccessToast(t('order.messages.deleteOrderSuccess'));
    // 关闭详情页
    emit("close");
  } catch (error) {
    showToast(t('order.messages.deleteOrderFailed'));
  }
};

// 下载装箱单
const downloadExcel_packingList = (item, order) => {
  apiService.utils.loadDown(
    apiService.urls.orderPackingListExcel +
      item.logisticsNumber +
      "&orderId=" +
      order.id
  );
};

// 下载订单详情
const downloadExcel = (item) => {
  apiService.utils.loadDown(apiService.urls.orderDetailExcel + item.id);
};

// 下载发票PDF
const downloadPdf = (item) => {
  apiService.order
    .generateInvoicePdf({ orderId: item.id })
    .then((res) => {
      if (res.code === "0") {
        const fileUrl = base_downFileByPath_url + res.msg;
        window.open(fileUrl, "_blank");
      } else {
        showToast(t('order.messages.getInvoiceFailed'));
      }
    })
    .catch((error) => {
      showToast(t('order.messages.downloadFailed') + ": " + error.message);
    });
};

// 获取商品图片URL
const getImageUrl = (item, product = null) => {
  let imageUrlList = [];

  // 如果只传入product参数，则只获取设备图片
  if (!item && product && product.productFileList && product.productFileList.length > 0) {
    // 取出product.productFileList里面后缀为图片类型的文件
    const imageFileList = product.productFileList.filter(
      (item) =>
        item.endsWith(".jpg") || item.endsWith(".png") || item.endsWith(".jpeg")
    );
    
    if (imageFileList.length > 0) {
      imageUrlList = imageFileList.map(
        (item) => base_downFileByPath_url + item
      );
      return imageUrlList;
    }
    
    // 如果没有找到图片，返回一个默认图片或空数组
    return ['/assets/images/default-product.png'];
  }
  
  // 从item的productFileList中获取图片
  if (item && item.productFileList && item.productFileList.length > 0) {
    // 处理不同的productFileList结构
    if (typeof item.productFileList[0] === "string") {
      imageUrlList.push(base_downFileByPath_url + item.productFileList[0]);
    } else if (item.productFileList[0] && item.productFileList[0].filePath) {
      imageUrlList.push(
        base_downFileByPath_url + item.productFileList[0].filePath
      );
    }
  }

  // 如果从item中没有找到图片并且有product参数，从product中获取图片
  if (
    imageUrlList.length === 0 && 
    product &&
    product.productFileList &&
    product.productFileList.length > 0
  ) {
    const imageFileList = product.productFileList.filter(
      (item) =>
        item.endsWith(".jpg") || item.endsWith(".png") || item.endsWith(".jpeg")
    );
    
    if (imageFileList.length > 0) {
      imageUrlList = imageFileList.map(
        (item) => base_downFileByPath_url + item
      );
    }
  }
  
  return imageUrlList.length > 0 ? imageUrlList : ['/assets/images/default-product.png'];
};

// 预览图片
const previewImage = (imageUrl) => {
  if (!imageUrl) return;
  showImagePreview(imageUrl);
};

// 检查单个商品是否有BOM数据
const hasItemBomData = (item) => {
  return !!(item.filePath || item.bomId || item.bomItemsId);
};

// 判断商品类型：整机或配件
const getItemType = (item) => {
  // 如果有BOM相关字段，通常表示这是一个配件
  if (hasItemBomData(item)) {
    return 'part';
  }

  // 检查商品类型字段
  if (item.productType) {
    return item.productType === 'part' ? 'part' : 'equipment';
  }

  // 检查商品名称或编码中是否包含配件相关关键词
  const name = (item.materielNameEn || item.materielName || '').toLowerCase();
  const code = (item.materielCode || '').toLowerCase();

  const partKeywords = ['part', 'component', 'spare', 'accessory', '配件', '零件', '部件'];
  const equipmentKeywords = ['machine', 'equipment', 'unit', 'system', '整机', '设备', '机器'];

  // 检查是否包含配件关键词
  if (partKeywords.some(keyword => name.includes(keyword) || code.includes(keyword))) {
    return 'part';
  }

  // 检查是否包含整机关键词
  if (equipmentKeywords.some(keyword => name.includes(keyword) || code.includes(keyword))) {
    return 'equipment';
  }

  // 默认根据是否有嵌套商品列表来判断
  // 如果商品有cartList或salesOrderItemList，通常是整机
  if ((item.cartList && item.cartList.length > 0) ||
      (item.salesOrderItemList && item.salesOrderItemList.length > 0)) {
    return 'equipment';
  }

  // 默认为配件
  return 'part';
};

// 获取商品类型标签文本
const getItemTypeLabel = (item) => {
  const type = getItemType(item);
  return type === 'equipment' ? t('order.productTypes.equipment') : t('order.productTypes.part');
};

// 获取商品类型样式类名
const getItemTypeClass = (item) => {
  const type = getItemType(item);
  return type === 'equipment' ? 'equipment-item' : 'part-item';
};

// 监听参数变化
watch(
  () => props.params.id,
  (newVal) => {
    if (newVal) {
      getOrderDetail();
    }
  },
  { immediate: true }
);

// 页面加载时获取数据
onMounted(() => {
  getOrderDetail();
});
</script>

<style lang="scss" scoped>
.order-detail-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 8px;
  padding-top: 6px;

  .loading-spinner {
    margin-top: 100px;
  }

  .status-card {
    display: flex;
    padding: 10px 12px;
    background-color: #fff;
    margin: 8px;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-top: 0;
    align-items: center;

    .status-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #f0f9eb;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      .van-icon {
        font-size: 16px;
        color: #07c160;
      }
    }

    .status-info {
      flex: 1;

      .status-title {
        margin-bottom: 4px;
      }

      .status-desc {
        color: #666;
        font-size: 12px;
        margin-bottom: 3px;
      }

      .status-date {
        font-size: 11px;
        color: #969799;
      }
    }
  }
  :deep(.van-cell-group__title) {
    padding-top: 0px;
    padding-bottom: 0px;
  }
  .info-group {
    margin: 6px 8px;
    border-radius: 6px;
    overflow: hidden;
  }

  .address-text {
    word-break: break-all;
    color: #666;
    font-size: 12px;
  }

  .price-label {
    font-size: 13px;
    color: #323233;
  }

  .price-value {
    font-size: 14px;
    font-weight: bold;
    color: #ee0a24;
  }

  .logistics-item {
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .logistics-info {
      padding: 6px 0;

      .logistics-company,
      .logistics-number {
        margin-bottom: 6px;
        font-size: 12px;
        color: #666;
      }

      .logistics-actions {
        display: flex;
        justify-content: flex-end;
      }
    }
  }

  .remark-text {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
  }

  .product-list-section {
    margin: 6px 8px;
    background-color: #fff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .section-header {
      padding: 8px 12px;
      border-bottom: 1px solid #f5f5f5;

      .section-title {
        font-size: 13px;
        font-weight: 500;
        color: #323233;
      }
    }

    .product-cards {
      padding: 4px;
    }

        .product-card {
      margin-bottom: 8px;
      border: 1px solid #ebedf0;
      border-radius: 6px;
      overflow: hidden;
      background-color: #fff;
      position: relative;
      transition: all 0.2s ease;

      &:last-child {
        margin-bottom: 0;
      }

      // 整机商品样式 - 使用深红色主题
      &.equipment-item {
        border-left: 4px solid #d32f2f;
        background: linear-gradient(135deg, #fef5f5 0%, #ffffff 100%);
        box-shadow: 0 2px 8px rgba(211, 47, 47, 0.1);
        border-left-style: solid;

        &:hover {
          box-shadow: 0 4px 12px rgba(211, 47, 47, 0.15);
          transform: translateY(-1px);
          border-left-width: 5px;
        }
      }

      // 配件商品样式 - 使用橙红色主题
      &.part-item {
        border-left: 4px solid #ff6b35;
        background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);
        border-left-style: solid;

        &:hover {
          box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
          transform: translateY(-1px);
          border-left-width: 5px;
        }
      }

      .product-card-content {
        display: flex;

        .product-image {
          width: 70px;
          height: 70px;
          flex-shrink: 0;
          position: relative;
          overflow: hidden;

          .van-image {
            width: 100%;
            height: 100%;
          }

          .image-placeholder {
            width: 100%;
            height: 100%;
            background-color: #f7f8fa;
            display: flex;
            align-items: center;
            justify-content: center;

            .van-icon {
              font-size: 20px;
              color: #dcdee0;
            }
          }
        }

        .product-info {
          flex: 1;
          padding: 6px 8px;
          position: relative;

          .product-header {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 4px;
            flex-wrap: wrap;

            .product-index {
              font-size: 11px;
              color: #969799;
              background-color: #f2f3f5;
              padding: 0px 4px;
              border-radius: 8px;
              flex-shrink: 0;
            }

            .product-type-tag {
              font-size: 10px;
              padding: 2px 6px;
              border-radius: 10px;
              font-weight: 500;
              flex-shrink: 0;

              &.equipment-tag {
                background-color: #ffebee;
                color: #d32f2f;
                border: 1px solid #ffcdd2;
              }

              &.part-tag {
                background-color: #fff3e0;
                color: #ff6b35;
                border: 1px solid #ffcc80;
              }
            }

            .product-code {
              font-size: 11px;
              color: #323233;
              flex: 1;
              text-align: right;
            }
          }

          .product-name {
            font-size: 13px;
            font-weight: 500;
            color: #323233;
            margin-bottom: 4px;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .product-price-info {
            display: flex;
            flex-wrap: wrap;
            font-size: 11px;
            position: relative;
            padding: 4px 0;

            .price-item {
              margin-right: 8px;
              margin-bottom: 2px;
              position: relative;
              transition: all 0.2s ease;

              .price-label {
                color: #969799;
                font-size: 11px;
                font-weight: normal;
              }

              .price-value {
                color: #323233;
                font-size: 11px;
                font-weight: normal;
                margin-left: 2px;
              }

              &.total-price {
                .price-label {
                  color: #ee0a24;
                }

                .price-value {
                  color: #ee0a24;
                  font-weight: 500;
                }
              }
            }
          }


            }
          }

          .product-actions {
            margin-top: 4px;
            display: flex;
            gap: 6px;
            flex-wrap: wrap;

            .bom-btn {
              border: 1px solid #e74c3c;
              color: #e74c3c;
              background: linear-gradient(135deg, #fff 0%, #fef5f5 100%);
              font-weight: 600;
              position: relative;
              overflow: hidden;
              transition: all 0.3s ease;

              &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.1), transparent);
                transition: left 0.5s ease;
              }

              &:hover {
                background: linear-gradient(135deg, #fdf2f2 0%, #fadbd8 100%);
                border-color: #c0392b;
                transform: translateY(-1px);
                box-shadow: 0 3px 8px rgba(231, 76, 60, 0.2);

                &::before {
                  left: 100%;
                }
              }

              &:active {
                background: #fadbd8;
                transform: translateY(0);
              }

              .van-icon {
                margin-right: 2px;
                transition: transform 0.2s ease;
              }

              &:hover .van-icon {
                transform: scale(1.1);
              }
            }
          }
        }
      }
    }

    .product-category {
      margin-bottom: 10px;
      
      // 产品分类下的商品卡片样式
      .product-card {
        margin-left: 10px;
        margin-right: 2px;
        margin-bottom: 6px;

        // 继承主要的类型区分样式
        &.equipment-item {
          border-left: 4px solid #d32f2f;
          background: linear-gradient(135deg, #fef5f5 0%, #ffffff 100%);
          box-shadow: 0 2px 8px rgba(211, 47, 47, 0.1);

          &:hover {
            box-shadow: 0 4px 12px rgba(211, 47, 47, 0.15);
            transform: translateY(-1px);
            border-left-width: 5px;
          }
        }

        &.part-item {
          border-left: 4px solid #ff6b35;
          background: linear-gradient(135deg, #fff8f5 0%, #ffffff 100%);
          box-shadow: 0 2px 8px rgba(255, 107, 53, 0.1);

          &:hover {
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
            transform: translateY(-1px);
            border-left-width: 5px;
          }
        }
      }

      .product-category-header {
        display: flex;
        align-items: center;
        background-color: #f2f3f5;
        border-radius: 4px;
        padding: 6px 8px;
        margin-bottom: 4px;
        
        .product-category-image {
          width: 48px;
          height: 48px;
          flex-shrink: 0;
          margin-right: 8px;
          overflow: hidden;
          border-radius: 3px;
          
          .van-image {
            width: 100%;
            height: 100%;
          }
          
          .image-placeholder {
            width: 100%;
            height: 100%;
            background-color: #f7f8fa;
            display: flex;
            align-items: center;
            justify-content: center;
            
            .van-icon {
              font-size: 20px;
              color: #dcdee0;
            }
          }
        }
        
        .product-category-title {
          font-size: 12px;
          font-weight: 500;
          color: #323233;
          flex: 1;
        }
      }
    }

    .empty-products {
      padding: 24px 0;
    }
 
  

  .action-buttons {
    margin: 12px 8px 16px;

    .van-button {
      margin-bottom: 8px;
    }

    .button-row {
      display: flex;
      gap: 8px;

      .van-button {
        flex: 1;
      }
    }
  }

  .remark-dialog-content {
    padding: 12px;
    font-size: 13px;
    color: #323233;
    line-height: 1.4;
    max-height: 240px;
    overflow-y: auto;
  }

  // BOM弹窗样式
  .bom-drawer-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #f7f8fa;

    .bom-drawer-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fff;
      border-bottom: 1px solid #ebedf0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .header-left {
        flex: 1;

        .drawer-title {
          font-size: 16px;
          font-weight: 600;
          color: #323233;
        }

        .bom-dialog-subtitle {
          font-size: 12px;
          color: #969799;
          margin: 4px 0 0 0;
        }
      }

      .header-right {
        .close-btn {
          background: #f2f3f5;
          border: none;
          color: #646566;

          &:hover {
            background: #e7e8ea;
          }
        }
      }
    }

    .bom-canvas-wrapper {
      flex: 1;
      overflow: hidden;
    }
  }
</style> 
