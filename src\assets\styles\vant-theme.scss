// Vant 主题定制文件
// 覆盖Vant默认主题色，使用偏红色系

:root {
  // 主题色
  --van-primary-color: #e74c3c;
  --van-success-color: #2ecc71;
  --van-danger-color: #c0392b;
  --van-warning-color: #f39c12;
  
  // 组件相关
  --van-button-primary-background: var(--van-primary-color);
  --van-button-primary-border-color: var(--van-primary-color);
  
  // 图标颜色
  --van-icon-active-color: var(--van-primary-color);
  
  // 单元格
  --van-cell-active-color: #fef2f0;
  
  // 复选框
  --van-checkbox-checked-icon-color: var(--van-primary-color);
  
  // 单选框
  --van-radio-checked-icon-color: var(--van-primary-color);
  
  // 开关
  --van-switch-on-background: var(--van-primary-color);
  
  // 标签栏
  --van-tabbar-item-active-color: var(--van-primary-color);
  
  // 标签页
  --van-tab-active-text-color: var(--van-primary-color);
  --van-tabs-bottom-bar-color: var(--van-primary-color);
} 