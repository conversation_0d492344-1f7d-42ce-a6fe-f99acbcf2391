<template>
  <van-popup
    v-model:show="visible"
    position="bottom"
    round
    closeable
    close-icon="close"
    :style="{ height: '80%' }"
    @close="handleClose"
  >
    <div class="tech-info-dialog">
      <div class="dialog-header">
        <h3>{{ $t('bom.techInfo') }}</h3>
        <p class="part-info">{{ currentPart?.materielNameEn }}</p>
        <p class="part-code">{{ currentPart?.materielCode }}</p>
      </div>
      
      <van-tabs v-model:active="activeTab" type="card" class="tech-tabs">
        <van-tab :title="$t('bom.fittedOn')" name="fittedOn">
          <div class="tab-content">
            <div v-if="usedInData.length > 0" class="fitted-list">
              <div v-for="(item, index) in usedInData" :key="index" class="fitted-item">
                <div class="fitted-header">
                  <van-icon name="apps-o" />
                  <span class="fitted-title">{{ $t('bom.fittedOnEquipment') }}</span>
                </div>
                <div class="fitted-content">
                  <p class="equipment-name">{{ item.materielNameEn }}</p>
                  <p class="equipment-code">{{ item.materielCode }}</p>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <van-icon name="info-o" />
              <p>{{ $t('bom.noFittedOnData') }}</p>
              <p class="empty-note">{{ $t('bom.fittedOnNote') }}</p>
            </div>
          </div>
        </van-tab>
        
        <van-tab :title="$t('bom.noticeOfChange')" name="noticeOfChange">
          <div class="tab-content">
            <div v-if="noticeData.length > 0" class="notice-list">
              <div v-for="(item, index) in noticeData" :key="index" class="notice-item">
                <div class="notice-header">
                  <van-icon name="warning-o" />
                  <span class="notice-date">{{ item.createDate }}</span>
                </div>
                <div class="notice-content">
                  <div v-if="item.files" class="notice-file" @click="openFile(item)">
                    <van-icon name="description" />
                    <span class="file-name">{{ getFileName(item.files) }}</span>
                  </div>
                  <p v-if="item.serialNumberRange" class="serial-range">
                    <span class="label">{{ $t('bom.serialNumberRange') }}:</span>
                    {{ item.serialNumberRange }}
                  </p>
                  <p v-if="item.description" class="description">
                    {{ item.description }}
                  </p>
                </div>
              </div>
            </div>
            <div v-else class="empty-state">
              <van-icon name="description" />
              <p>{{ $t('bom.noNoticeData') }}</p>
              <p class="empty-note">{{ $t('bom.noticeNote') }}</p>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  currentPart: {
    type: Object,
    default: null
  },
  usedInData: {
    type: Array,
    default: () => []
  },
  noticeData: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:show', 'close']);

// 响应式数据
const activeTab = ref('fittedOn');

// 计算属性
const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

// 方法
const handleClose = () => {
  emit('close');
};

const getFileName = (filePath) => {
  if (!filePath) return '';
  return filePath.split('/').pop();
};

const openFile = (item) => {
  if (!item.files) return;
  
  const currentDomain = window.location.origin;
  const fullUrl = `${currentDomain}/fileviewer/${item.files}`;
  window.open(fullUrl, '_blank');
};
</script>

<style lang="scss" scoped>
.tech-info-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .dialog-header {
    padding: 16px;
    border-bottom: 1px solid #ebedf0;
    background: #f7f8fa;
    
    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #323233;
    }
    
    .part-info {
      margin: 0 0 4px 0;
      font-size: 14px;
      font-weight: 500;
      color: #646566;
    }
    
    .part-code {
      margin: 0;
      font-size: 12px;
      color: #969799;
    }
  }
  
  .tech-tabs {
    flex: 1;

    :deep(.van-tabs__nav) {
      background: #f7f8fa;
    }

    :deep(.van-tab) {
      color: #646566;
      font-weight: 500;
      transition: all 0.2s ease;

      &.van-tab--active {
        color: #ffffff;
        font-weight: 700;
        background: linear-gradient(135deg, #ee0a24, #ff4757);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      &:hover:not(.van-tab--active) {
        color: #ee0a24;
        background: rgba(238, 10, 36, 0.05);
      }
    }

    :deep(.van-tabs__line) {
      background: linear-gradient(135deg, #ee0a24, #ff4757);
      height: 3px;
      border-radius: 2px;
    }

    :deep(.van-tabs__nav--card) {
      .van-tab {
        border: 1px solid #ebedf0;
        border-radius: 6px 6px 0 0;
        margin-right: 4px;

        &.van-tab--active {
          border-color: #ee0a24;
          border-bottom-color: transparent;
          position: relative;
          z-index: 1;
          box-shadow: 0 2px 8px rgba(238, 10, 36, 0.2);
        }
      }
    }

    :deep(.van-tabs__content) {
      height: calc(100% - 44px);
      overflow: hidden;
    }

    :deep(.van-tab__panel) {
      height: 100%;
    }
  }
  
  .tab-content {
    height: 100%;
    overflow-y: auto;
    padding: 16px;
  }
  
  .fitted-list, .notice-list {
    .fitted-item, .notice-item {
      background: white;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .fitted-header, .notice-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        
        .van-icon {
          margin-right: 8px;
          color: #ee0a24;
          font-size: 16px;
        }
        
        .fitted-title {
          font-weight: 600;
          color: #323233;
        }
        
        .notice-date {
          font-size: 12px;
          color: #646566;
        }
      }
      
      .fitted-content {
        padding-left: 24px;
        
        .equipment-name {
          margin: 0 0 4px 0;
          font-weight: 500;
          color: #323233;
        }
        
        .equipment-code {
          margin: 0;
          font-size: 12px;
          color: #646566;
        }
      }
      
      .notice-content {
        padding-left: 24px;
        
        .notice-file {
          display: flex;
          align-items: center;
          padding: 8px;
          background: #f7f8fa;
          border-radius: 4px;
          margin-bottom: 8px;
          cursor: pointer;
          
          &:hover {
            background: #ebedf0;
          }
          
          .van-icon {
            margin-right: 8px;
            color: #1989fa;
          }
          
          .file-name {
            color: #1989fa;
            text-decoration: underline;
          }
        }
        
        .serial-range, .description {
          margin: 4px 0;
          font-size: 12px;
          line-height: 1.4;
          
          .label {
            font-weight: 500;
            text-decoration: underline;
          }
        }
        
        .description {
          color: #646566;
        }
      }
    }
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #969799;
    
    .van-icon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #c8c9cc;
    }
    
    p {
      margin: 4px 0;
      text-align: center;
    }
    
    .empty-note {
      font-size: 12px;
      color: #c8c9cc;
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 600px) {
  .tech-info-dialog {
    .dialog-header {
      padding: 12px 16px;
      
      h3 {
        font-size: 16px;
        margin-bottom: 4px;
      }
      
      .part-info {
        font-size: 12px;
      }
      
      .part-code {
        font-size: 11px;
      }
    }
    
    .tab-content {
      padding: 12px;
    }
    
    .fitted-list, .notice-list {
      .fitted-item, .notice-item {
        padding: 8px;
        margin-bottom: 8px;
      }
    }
    
    .empty-state {
      height: 150px;
      
      .van-icon {
        font-size: 36px;
        margin-bottom: 12px;
      }
    }
  }
}
</style>
