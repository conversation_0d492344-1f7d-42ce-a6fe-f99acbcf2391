// 主题颜色
$primary-color: #e74c3c; // 主色调 - 改为红色
$success-color: #2ecc71; // 成功
$warning-color: #f39c12; // 警告
$danger-color: #c0392b;  // 危险 - 深红色
$info-color: #95a5a6;    // 信息

// 文本颜色
$text-color-primary: #212121;   // 主要文本
$text-color-regular: #606266;   // 常规文本
$text-color-secondary: #8c8c8c; // 次要文本
$text-color-placeholder: #c0c4cc; // 占位文本

// 边框和背景
$border-color: #ebedf0;
$background-color: #f7f8fa;

// 字体大小
$font-size-extra-large: 20px;
$font-size-large: 18px;
$font-size-medium: 16px;
$font-size-base: 14px;
$font-size-small: 13px;
$font-size-extra-small: 12px;

// 间距
$spacing-extra-large: 32px;
$spacing-large: 24px;
$spacing-medium: 16px;
$spacing-base: 12px;
$spacing-small: 8px;
$spacing-extra-small: 4px;

// 布局相关
$header-height: 46px;
$tabbar-height: 50px;

// 方向特定变量
:root {
  // 竖屏模式下的变量
  --portrait-sidebar-width: 0px;
  --portrait-content-padding: #{$spacing-base};
  
  // 横屏模式下的变量
  --landscape-sidebar-width: 200px;
  --landscape-content-padding: #{$spacing-medium};
  
  // 主题颜色变量
  --primary-color: #{$primary-color};
  --success-color: #{$success-color};
  --warning-color: #{$warning-color};
  --danger-color: #{$danger-color};
  --info-color: #{$info-color};
}

// 媒体查询断点
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px; 