<template>
  <div class="app-logo" :class="{ 'app-logo--small': small }">
    <div class="logo-container">
      <img :src="currentLogoSrc" :alt="logoAlt" class="logo-image" />
    </div>
    <div class="logo-text" v-if="showTitle">
      <p class="app-name">{{ currentTitle }}</p>
      <p class="app-slogan" v-if="currentSlogan">{{ currentSlogan }}</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useConfig } from '@/composables/useConfig'

// 导入不同语言的logo图片
import logoZhCN from '@/assets/images/main/logo_turfone.png' // 中文logo
import logoEn from '@/assets/images/main/logo_turfone_en.png' // 英文logo

const { locale, t } = useI18n()
const { getConfig } = useConfig()

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  slogan: {
    type: String,
    default: ''
  },
  logoSrc: {
    type: String,
    default: ''
  },
  small: {
    type: Boolean,
    default: false
  },
  showTitle: {
    type: Boolean,
    default: true
  },
  // 是否使用多语言logo
  useI18nLogo: {
    type: Boolean,
    default: true
  }
})

// 根据当前语言计算logo路径
const currentLogoSrc = computed(() => {
  // 如果传入了自定义logo，优先使用
  if (props.logoSrc) {
    return props.logoSrc
  }

  // 如果启用多语言logo
  if (props.useI18nLogo) {
    // 优先从配置系统获取logo路径
    const configLogoPath = getConfig('appConfig.logoPath')
    if (configLogoPath) {
      // 配置路径是相对路径，需要转换为实际的导入路径
      return locale.value === 'zh-CN' ? logoZhCN : logoEn
    }

    // 回退到硬编码的logo
    return locale.value === 'zh-CN' ? logoZhCN : logoEn
  }

  // 默认使用中文logo
  return logoZhCN
})

// 根据当前语言计算标题
const currentTitle = computed(() => {
  if (props.title) {
    return props.title
  }

  // 优先从配置系统获取
  const configAppName = getConfig('appConfig.name')
  if (configAppName) {
    return configAppName
  }

  // 回退到i18n翻译
  return t('common.appName') || 'ERPs Parts'
})

// 根据当前语言计算标语
const currentSlogan = computed(() => {
  if (props.slogan) {
    return props.slogan
  }

  // 优先从配置系统获取
  const configSlogan = getConfig('appConfig.slogan')
  if (configSlogan) {
    return configSlogan
  }

  // 回退到i18n翻译
  return t('common.slogan') || '配件管理系统'
})

// logo的alt文本
const logoAlt = computed(() => {
  return `${currentTitle.value} Logo`
})
</script>

<style lang="scss" scoped>
.app-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  &--small {
    flex-direction: row;
    align-items: center;
    
    .logo-container {
      width: 36px;
      height: 36px;
      margin-right: 10px;
      margin-bottom: 0;
    }
    
    .logo-text {
      text-align: left;
      
      .app-name {
        font-size: 16px;
        margin-bottom: 0;
      }
      
      .app-slogan {
        font-size: 12px;
      }
    }
  }
  
  .logo-container {
    width: 70px;
    height: 70px;
    margin-bottom: 4px;
    
    .logo-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  
  .logo-text {
    text-align: center;
    
    .app-name {
      font-size: 22px;
      font-weight: bold;
      margin: 0 0 0;
      color: inherit;
    }
    
    .app-slogan {
      font-size: 14px;
      margin: 0;
      color: inherit;
      opacity: 0.8;
    }
  }
}
</style> 