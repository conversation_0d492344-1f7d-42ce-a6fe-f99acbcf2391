import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useOrientationStore = defineStore('orientation', () => {
  // 状态
  const orientation = ref('portrait') // 'portrait' 或 'landscape'
  
  // 计算属性
  const isPortrait = computed(() => orientation.value === 'portrait')
  const isLandscape = computed(() => orientation.value === 'landscape')
  
  // 动作
  function setOrientation(newOrientation) {
    if (['portrait', 'landscape'].includes(newOrientation)) {
      orientation.value = newOrientation
      // 存储到localStorage
      localStorage.setItem('preferred-orientation', newOrientation)
    }
  }
  
  // 初始化方向
  function initOrientation() {
    // 首先检查localStorage中是否有用户首选方向
    const savedOrientation = localStorage.getItem('preferred-orientation')
    if (savedOrientation && ['portrait', 'landscape'].includes(savedOrientation)) {
      orientation.value = savedOrientation
    } else {
      // 检测当前设备方向
      updateOrientationByDevice()
      // 添加屏幕方向变化监听器
      setupOrientationListener()
    }
  }
  
  // 更新屏幕方向
  function updateOrientationByDevice() {
    // 使用window.matchMedia来检测设备方向
    const isLandscapeMode = window.matchMedia('(orientation: landscape)').matches
    orientation.value = isLandscapeMode ? 'landscape' : 'portrait'
  }
  
  // 设置屏幕方向变化监听器
  function setupOrientationListener() {
    const mediaQuery = window.matchMedia('(orientation: landscape)')
    
    // 定义监听回调
    const handleOrientationChange = (event) => {
      orientation.value = event.matches ? 'landscape' : 'portrait'
    }
    
    // 添加监听器
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleOrientationChange)
    } else if (mediaQuery.addListener) {
      // 兼容旧版浏览器
      mediaQuery.addListener(handleOrientationChange)
    }
  }
  
  return {
    orientation,
    isPortrait,
    isLandscape,
    setOrientation,
    initOrientation,
    updateOrientationByDevice
  }
}, {
  persist: true
}) 