<template>
  <div class="file-preview">
    <!-- PDF 预览 -->
    <div v-if="fileType === 'pdf'" class="pdf-preview">
      <div class="pdf-header">
        <h3 class="pdf-title">{{ $t('file.pdfHandbook') }}</h3>
        <div class="pdf-actions">
          <van-button 
            type="primary" 
            size="small"
            icon="eye-o"
            @click="previewPDF"
            :loading="previewLoading"
          >
            {{ $t('file.previewOnline') }}
          </van-button>
          <van-button 
            type="default" 
            size="small"
            icon="down"
            @click="downloadFile"
            :loading="downloadLoading"
          >
            {{ $t('file.download') }}
          </van-button>
        </div>
      </div>
      
      <!-- PDF 预览容器 -->
      <div v-if="showPdfViewer" class="pdf-viewer">
        <!-- PDF 工具栏 -->
        <div class="pdf-toolbar">
          <div class="toolbar-left">
            <van-button
              size="small"
              icon="arrow-left"
              @click="prevPage"
              :disabled="currentPage <= 1"
            />
            <span class="page-info">
              {{ currentPage }} / {{ totalPages }}
            </span>
            <van-button
              size="small"
              icon="arrow"
              @click="nextPage"
              :disabled="currentPage >= totalPages"
            />
          </div>
          <div class="toolbar-right">
            <van-button
              size="small"
              icon="minus"
              @click="zoomOut"
              :disabled="scale <= 0.5"
            />
            <span class="zoom-info">{{ Math.round(scale * 100) }}%</span>
            <van-button
              size="small"
              icon="plus"
              @click="zoomIn"
              :disabled="scale >= 3"
            />
            <van-button
              size="small"
              icon="replay"
              @click="resetZoom"
            />
          </div>
        </div>

        <!-- PDF 画布容器 -->
        <div class="pdf-canvas-container" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
          <canvas ref="pdfCanvas" class="pdf-canvas"></canvas>
        </div>

        <!-- 页面跳转 -->
        <div class="page-jump">
          <van-field
            v-model="jumpPageNumber"
            type="number"
            :placeholder="$t('file.jumpToPage')"
            @keyup.enter="jumpToPage"
          >
            <template #button>
              <van-button size="small" type="primary" @click="jumpToPage">
                {{ $t('file.jump') }}
              </van-button>
            </template>
          </van-field>
        </div>
      </div>
      
      <!-- PDF 信息卡片 -->
      <div v-else class="pdf-info">
        <van-icon name="description" size="48" color="#1989fa" />
        <h4>{{ $t('file.pdfDocument') }}</h4>
        <p>{{ $t('file.pdfDescription') }}</p>
      </div>
    </div>

    <!-- 视频预览 -->
    <div v-else-if="fileType === 'video'" class="video-preview">
      <div class="video-header">
        <h3 class="video-title">{{ $t('file.videoFile') }}</h3>
        <div class="video-actions">
          <van-button
            type="default"
            size="small"
            icon="down"
            @click="downloadFile"
            :loading="downloadLoading"
          >
            {{ $t('file.download') }}
          </van-button>
        </div>
      </div>

      <div class="video-container">
        <video
          ref="videoPlayer"
          :src="fullFilePath"
          controls
          preload="metadata"
          class="video-player"
          :poster="videoPoster"
          @loadstart="handleVideoLoadStart"
          @loadeddata="handleVideoLoaded"
          @error="handleVideoError"
          @play="handleVideoPlay"
          @pause="handleVideoPause"
          @timeupdate="handleTimeUpdate"
          @ended="handleVideoEnded"
          playsinline
          webkit-playsinline
        >
          {{ $t('file.videoNotSupported') }}
        </video>

        <!-- 视频加载状态 -->
        <div v-if="videoLoading" class="video-loading">
          <van-loading size="24px" vertical>
            {{ $t('file.loadingVideo') }}
          </van-loading>
        </div>

        <!-- 视频错误状态 -->
        <div v-if="videoError" class="video-error">
          <van-icon name="warning-o" size="32" color="#ee0a24" />
          <p>{{ $t('file.videoLoadFailed') }}</p>
          <van-button size="small" @click="retryVideo">
            {{ $t('file.retry') }}
          </van-button>
        </div>

        <!-- 视频信息 -->
        <div v-if="videoInfo.duration && !videoLoading && !videoError" class="video-info">
          <div class="video-stats">
            <span>{{ $t('file.duration') }}: {{ formatDuration(videoInfo.duration) }}</span>
            <span v-if="videoInfo.size">{{ $t('file.fileSize') }}: {{ formatFileSize(videoInfo.size) }}</span>
          </div>
          <div class="video-progress">
            <van-progress
              :percentage="videoProgress"
              stroke-width="4"
              color="#1989fa"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 其他文件类型 -->
    <div v-else class="other-file">
      <div class="file-info">
        <van-icon :name="getFileIcon()" size="48" :color="getFileIconColor()" />
        <h4>{{ getFileName() }}</h4>
        <div class="file-details">
          <p>{{ $t('file.fileType') }}: {{ getFileExtension() }}</p>
          <p v-if="fileSize">{{ $t('file.fileSize') }}: {{ formatFileSize(fileSize) }}</p>
          <p v-if="lastModified">{{ $t('file.lastModified') }}: {{ formatDate(lastModified) }}</p>
        </div>

        <!-- 文件预览（如果支持） -->
        <div v-if="canPreview" class="file-preview-area">
          <van-button
            type="primary"
            icon="eye-o"
            @click="previewFile"
            :loading="previewLoading"
          >
            {{ $t('file.preview') }}
          </van-button>
        </div>

        <div class="file-actions">
          <van-button
            type="default"
            icon="down"
            @click="downloadFile"
            :loading="downloadLoading"
          >
            {{ $t('file.download') }}
          </van-button>
          <van-button
            type="default"
            icon="share-o"
            @click="shareFile"
          >
            {{ $t('file.share') }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { showNotify } from 'vant'
import { BASE_FILE_URL } from '@/config/config'
import apiService from '@/utils/api'

// PDF.js 相关导入
import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist/legacy/build/pdf'
import * as pdfjs from 'pdfjs-dist'

// 设置 PDF.js worker
GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.mjs`

const { t } = useI18n()

const props = defineProps({
  filePath: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: 'other' // 'pdf', 'video', 'other'
  }
})

// 响应式数据
const previewLoading = ref(false)
const downloadLoading = ref(false)
const videoLoading = ref(false)
const videoError = ref(false)
const showPdfViewer = ref(false)

// PDF 相关
const pdfCanvas = ref(null)
const pdfDoc = ref(null)
const currentPage = ref(1)
const totalPages = ref(0)
const scale = ref(1)
const jumpPageNumber = ref('')

// 视频相关
const videoPlayer = ref(null)
const videoPoster = ref('')
const videoInfo = ref({
  duration: 0,
  size: 0,
  currentTime: 0
})
const videoProgress = ref(0)

// 触摸相关
const touchStartX = ref(0)
const touchStartY = ref(0)
const lastTouchTime = ref(0)

// 文件信息
const fileSize = ref(0)
const lastModified = ref('')

// 计算属性
const fullFilePath = computed(() => {
  if (!props.filePath) return ''
  return BASE_FILE_URL + props.filePath
})

const canPreview = computed(() => {
  const extension = getFileExtension().toLowerCase()
  return ['txt', 'json', 'xml', 'csv'].includes(extension)
})

const fileIcon = computed(() => {
  return getFileIcon()
})

const fileIconColor = computed(() => {
  return getFileIconColor()
})

// 方法定义
const getFileName = () => {
  if (!props.filePath) return t('file.unknownFile')
  const parts = props.filePath.split('/')
  return parts[parts.length - 1] || t('file.unknownFile')
}

const getFileExtension = () => {
  const fileName = getFileName()
  const parts = fileName.split('.')
  return parts.length > 1 ? parts[parts.length - 1].toUpperCase() : 'UNKNOWN'
}

const getFileIcon = () => {
  const extension = getFileExtension().toLowerCase()
  const iconMap = {
    pdf: 'description',
    mp4: 'video-o',
    avi: 'video-o',
    mov: 'video-o',
    txt: 'notes-o',
    doc: 'notes-o',
    docx: 'notes-o',
    xls: 'notes-o',
    xlsx: 'notes-o',
    ppt: 'notes-o',
    pptx: 'notes-o',
    zip: 'bag-o',
    rar: 'bag-o',
    jpg: 'photo-o',
    jpeg: 'photo-o',
    png: 'photo-o',
    gif: 'photo-o'
  }
  return iconMap[extension] || 'notes-o'
}

const getFileIconColor = () => {
  const extension = getFileExtension().toLowerCase()
  const colorMap = {
    pdf: '#ff6b35',
    mp4: '#ee0a24',
    avi: '#ee0a24',
    mov: '#ee0a24',
    txt: '#1989fa',
    doc: '#1989fa',
    docx: '#1989fa',
    xls: '#07c160',
    xlsx: '#07c160',
    ppt: '#ff976a',
    pptx: '#ff976a',
    zip: '#ffd21e',
    rar: '#ffd21e',
    jpg: '#07c160',
    jpeg: '#07c160',
    png: '#07c160',
    gif: '#07c160'
  }
  return colorMap[extension] || '#969799'
}

const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatFileSize = (bytes) => {
  if (!bytes || isNaN(bytes)) return '0 B'
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString()
}

const previewFile = () => {
  // 实现文本文件预览
  previewLoading.value = true
  // 这里可以添加文本文件预览逻辑
  setTimeout(() => {
    previewLoading.value = false
    showNotify({ type: 'success', message: t('file.previewNotSupported') })
  }, 1000)
}

const shareFile = () => {
  if (navigator.share) {
    navigator.share({
      title: getFileName(),
      url: fullFilePath.value
    }).catch(err => {
      console.error('分享失败:', err)
      copyToClipboard()
    })
  } else {
    copyToClipboard()
  }
}

const copyToClipboard = () => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(fullFilePath.value).then(() => {
      showNotify({ type: 'success', message: t('file.linkCopied') })
    }).catch(() => {
      showNotify({ type: 'warning', message: t('file.copyFailed') })
    })
  } else {
    showNotify({ type: 'warning', message: t('file.shareNotSupported') })
  }
}

const previewPDF = async () => {
  if (!props.filePath) {
    showNotify({ type: 'warning', message: t('file.invalidFilePath') })
    return
  }

  previewLoading.value = true
  try {
    const loadingTask = getDocument(fullFilePath.value)
    pdfDoc.value = await loadingTask.promise
    totalPages.value = pdfDoc.value.numPages
    currentPage.value = 1
    showPdfViewer.value = true
    
    await renderPage(1)
    showNotify({ type: 'success', message: t('file.pdfLoadSuccess') })
  } catch (error) {
    showNotify({ type: 'danger', message: t('file.pdfLoadFailed') })
  } finally {
    previewLoading.value = false
  }
}

const renderPage = async (pageNum) => {
  if (!pdfDoc.value || !pdfCanvas.value) return

  try {
    const page = await pdfDoc.value.getPage(pageNum)
    const viewport = page.getViewport({ scale: scale.value })
    
    const canvas = pdfCanvas.value
    const context = canvas.getContext('2d')
    
    canvas.height = viewport.height
    canvas.width = viewport.width
    
    const renderContext = {
      canvasContext: context,
      viewport: viewport
    }
    
    await page.render(renderContext).promise
    currentPage.value = pageNum
  } catch (error) {
    console.error('PDF 页面渲染失败:', error)
    showNotify({ type: 'danger', message: t('file.pdfRenderFailed') })
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    renderPage(currentPage.value - 1)
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    renderPage(currentPage.value + 1)
  }
}

const zoomIn = () => {
  if (scale.value < 3) {
    scale.value += 0.25
    renderPage(currentPage.value)
  }
}

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value -= 0.25
    renderPage(currentPage.value)
  }
}

const resetZoom = () => {
  scale.value = 1
  renderPage(currentPage.value)
}

const jumpToPage = () => {
  const pageNum = parseInt(jumpPageNumber.value)
  if (pageNum >= 1 && pageNum <= totalPages.value) {
    renderPage(pageNum)
    jumpPageNumber.value = ''
  } else {
    showNotify({ type: 'warning', message: t('file.invalidPageNumber') })
  }
}

// 触摸事件处理
const handleTouchStart = (e) => {
  const touch = e.touches[0]
  touchStartX.value = touch.clientX
  touchStartY.value = touch.clientY
  lastTouchTime.value = Date.now()
}

const handleTouchMove = (e) => {
  e.preventDefault() // 防止页面滚动
}

const handleTouchEnd = (e) => {
  const touch = e.changedTouches[0]
  const deltaX = touch.clientX - touchStartX.value
  const deltaY = touch.clientY - touchStartY.value
  const deltaTime = Date.now() - lastTouchTime.value

  // 检测滑动手势
  if (Math.abs(deltaX) > 50 && Math.abs(deltaY) < 100 && deltaTime < 300) {
    if (deltaX > 0) {
      // 向右滑动，上一页
      prevPage()
    } else {
      // 向左滑动，下一页
      nextPage()
    }
  }

  // 检测双击
  if (deltaTime < 300 && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
    const now = Date.now()
    if (now - lastTouchTime.value < 300) {
      // 双击缩放
      if (scale.value === 1) {
        scale.value = 2
      } else {
        scale.value = 1
      }
      renderPage(currentPage.value)
    }
  }
}

const downloadFile = async () => {
  if (!props.filePath) {
    showNotify({ type: 'warning', message: t('file.invalidFilePath') })
    return
  }

  downloadLoading.value = true
  try {
    // 使用 API 服务的下载方法
    await apiService.utils.previewFile(props.filePath)
  } catch (error) {
    showNotify({ type: 'danger', message: t('file.downloadFailed') })
  } finally {
    downloadLoading.value = false
  }
}

// 视频相关方法
const handleVideoLoadStart = () => {
  videoLoading.value = true
  videoError.value = false
}

const handleVideoLoaded = () => {
  videoLoading.value = false
  videoError.value = false
}

const handleVideoError = () => {
  videoLoading.value = false
  videoError.value = true
  showNotify({ type: 'danger', message: t('file.videoLoadFailed') })
}

const handleVideoPlay = () => {
  console.log('视频开始播放')
}

const handleVideoPause = () => {
  console.log('视频暂停')
}

const handleTimeUpdate = () => {
  if (videoPlayer.value) {
    const current = videoPlayer.value.currentTime
    const duration = videoPlayer.value.duration
    videoInfo.value.currentTime = current
    videoProgress.value = duration > 0 ? (current / duration) * 100 : 0
  }
}

const handleVideoEnded = () => {
  console.log('视频播放结束')
  videoProgress.value = 100
}

const retryVideo = () => {
  videoError.value = false
  videoLoading.value = true
  if (videoPlayer.value) {
    videoPlayer.value.load()
  }
}

// 监听文件路径变化
watch(() => props.filePath, () => {
  // 重置状态
  showPdfViewer.value = false
  videoLoading.value = false
  videoError.value = false
  pdfDoc.value = null
  currentPage.value = 1
  totalPages.value = 0
  scale.value = 1
}, { immediate: true })

// 生命周期
onMounted(() => {
  // 组件挂载时的初始化
})

onUnmounted(() => {
  // 清理 PDF 文档
  if (pdfDoc.value) {
    pdfDoc.value.destroy()
  }
})
</script>

<style scoped>
.file-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

/* PDF 预览样式 */
.pdf-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
  background: white;
}

.pdf-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.pdf-actions {
  display: flex;
  gap: 8px;
}

.pdf-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pdf-toolbar {
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pdf-canvas-container {
  flex: 1;
  overflow: auto;
  background: #f8f9fa;
  position: relative;
  touch-action: none;
}

.pdf-canvas {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-jump {
  padding: 8px 16px;
  background: white;
  border-top: 1px solid #eee;
}

.page-info,
.zoom-info {
  font-size: 14px;
  color: #666;
  margin: 0 8px;
}

.pdf-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
}

.pdf-info h4 {
  margin: 16px 0 8px 0;
  font-size: 16px;
  color: #333;
}

.pdf-info p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

/* 视频预览样式 */
.video-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
  background: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.video-actions {
  display: flex;
  gap: 8px;
}

.video-container {
  flex: 1;
  position: relative;
  background: #000;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-loading,
.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
}

.video-error p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.video-info {
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #eee;
}

.video-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.video-progress {
  margin-top: 8px;
}

/* 其他文件样式 */
.other-file {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-info {
  text-align: center;
  padding: 32px 16px;
}

.file-info h4 {
  margin: 16px 0 8px 0;
  font-size: 16px;
  color: #333;
}

.file-details {
  margin: 12px 0;
}

.file-details p {
  margin: 4px 0;
  font-size: 14px;
  color: #666;
}

.file-preview-area {
  margin: 16px 0;
}

.file-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-top: 20px;
}

/* 触摸优化 */
.van-button {
  -webkit-tap-highlight-color: transparent;
  transition: transform 0.1s ease;
}

.van-button:active {
  transform: scale(0.98);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .pdf-header,
  .video-header {
    padding: 12px;
  }

  .pdf-actions {
    gap: 6px;
  }

  .pdf-controls {
    padding: 8px 12px;
    gap: 6px;
  }

  .file-info {
    padding: 24px 12px;
  }
}

@media (orientation: landscape) {
  .pdf-controls {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 100;
  }

  .pdf-viewer {
    padding-bottom: 60px;
  }
}
</style>
