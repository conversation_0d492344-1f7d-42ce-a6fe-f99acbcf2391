@use './variables.scss' as *;
@use './mixins.scss' as *;

/* 全局重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-size: $font-size-base;
  color: $text-color-primary;
  background-color: #f5f5f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
}

/* 横屏适配 */
@include landscape {
  :root {
    --content-padding: var(--landscape-content-padding);
  }
  
  .landscape-only {
    display: block;
  }
  
  .portrait-only {
    display: none;
  }
}

/* 竖屏适配 */
@include portrait {
  :root {
    --content-padding: var(--portrait-content-padding);
  }
  
  .landscape-only {
    display: none;
  }
  
  .portrait-only {
    display: block;
  }
}

/* 通用样式类 */
.page {
  padding: var(--content-padding, 12px);
  height: 100%;
  width: 100%;
  overflow-y: auto;
}

.flex-center {
  @include flex(row, center, center);
}

.flex-between {
  @include flex(row, space-between, center);
}

.flex-column {
  @include flex(column);
}

.text-ellipsis {
  @include ellipsis(1);
}

/* 安全区适配 */
.safe-area-bottom {
  @include safe-area-inset(bottom, padding);
}

.safe-area-top {
  @include safe-area-inset(top, padding);
} 