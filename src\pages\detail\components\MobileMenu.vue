<template>
  <div class="mobile-menu">
    <!-- 搜索栏 -->
    <div class="search-section">
      <van-search
        v-model="searchKeyword"
        :placeholder="$t('detail.searchMenu')"
        @search="handleSearch"
        @clear="handleClearSearch"
        show-action
        size="small"
      >
        <template #action>
          <div @click="handleSearch">{{ $t('common.search') }}</div>
        </template>
      </van-search>
    </div>

    <!-- 菜单内容 -->
    <div class="menu-container">
      <!-- 搜索结果 -->
      <div v-if="isSearching" class="search-results">
        <div v-if="searchResults.length === 0" class="no-results">
          <van-empty
            :description="$t('detail.noSearchResults')"
            image="search"
          />
        </div>
        <div v-else class="search-list">
          <div
            v-for="item in searchResults"
            :key="item.id"
            class="search-item"
            @click="handleMenuItemClick(item)"
          >
            <div class="item-icon">
              <van-icon
                :name="getItemIcon(item)"
                size="16"
                :color="getItemIconColor(item)"
              />
            </div>
            <div class="item-content">
              <div class="item-name" v-html="highlightSearchText(item.fileName)"></div>
              <div class="item-path">{{ getItemPath(item) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 正常菜单导航 -->
      <div v-else class="normal-menu">
        <!-- 面包屑导航和返回按钮 -->
        <div v-if="currentPath.length > 0" class="breadcrumb">
          <!-- 返回按钮 -->
          <div class="breadcrumb-back" @click="goBack">
            <van-icon name="arrow-left" size="14" />
          </div>

          <!-- 面包屑路径 -->
          <div class="breadcrumb-path">
            <div class="breadcrumb-item" @click="goToRoot">
              <van-icon name="wap-home-o" size="12" />
              <span>{{ $t('detail.menu') }}</span>
            </div>
            <van-icon name="arrow" size="10" class="breadcrumb-separator" />
            <div
              v-for="(pathItem, index) in currentPath"
              :key="pathItem.id"
              class="breadcrumb-item"
              @click="goToPath(index)"
            >
              <span>{{ removeFileExtension(pathItem.fileName) }}</span>
              <van-icon
                v-if="index < currentPath.length - 1"
                name="arrow"
                size="10"
                class="breadcrumb-separator"
              />
            </div>
          </div>
        </div>

        <!-- 菜单统计信息 -->
        <div v-if="currentItems.length > 0" class="menu-stats">
          <span class="stats-text">
            {{ $t('detail.totalItems', { count: currentItems.length }) }}
            <span v-if="folderCount > 0">
              · {{ $t('detail.folders', { count: folderCount }) }}
            </span>
            <span v-if="fileCount > 0">
              · {{ $t('detail.files', { count: fileCount }) }}
            </span>
          </span>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="menu-loading">
          <van-loading size="20px" vertical>
            {{ $t('detail.loadingMenu') }}
          </van-loading>
        </div>

        <!-- 菜单内容 -->
        <div v-else-if="currentItems.length > 0" class="menu-content">
          <div
            v-for="item in currentItems"
            :key="item.id"
            class="menu-item"
            :class="{ 'menu-item--highlighted': item.id === highlightedItemId }"
            @click="handleMenuItemClick(item)"
          >
            <div class="item-icon">
              <van-icon
                :name="getItemIcon(item)"
                size="16"
                :color="getItemIconColor(item)"
              />
            </div>
            <div class="item-content">
              <div class="item-name">{{ removeFileExtension(item.fileName) }}</div>
              <div v-if="item.children && item.children.length" class="item-count">
                {{ $t('detail.itemCount', { count: item.children.length }) }}
              </div>
            </div>
            <div class="item-actions">
              <div v-if="item.children && item.children.length" class="item-arrow">
                <van-icon name="arrow" size="12" />
              </div>
              <div v-else class="item-type">
                {{ getFileTypeText(item) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-menu">
          <van-empty
            :description="$t('detail.emptyMenu')"
            image="search"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  menuData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  // 新增：需要展开到的文件路径
  expandToPath: {
    type: String,
    default: ''
  },
  // 新增：需要高亮的菜单项ID
  highlightItemId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['menu-click'])

// 响应式数据
const currentPath = ref([])
const searchKeyword = ref('')
const isSearching = ref(false)
const searchResults = ref([])
const allMenuItems = ref([])

// 计算属性：高亮显示的菜单项ID（优先使用props，其次使用内部状态）
const highlightedItemId = computed(() => {
  return props.highlightItemId || internalHighlightedItemId.value
})

// 内部高亮状态（用于用户交互时的临时高亮）
const internalHighlightedItemId = ref(null)

const currentItems = computed(() => {
  if (currentPath.value.length === 0) {
    return props.menuData
  }

  let items = props.menuData
  for (const pathItem of currentPath.value) {
    const found = items.find(item => item.id === pathItem.id)
    if (found && found.children) {
      items = found.children
    } else {
      break
    }
  }
  return items
})

const folderCount = computed(() => {
  return currentItems.value.filter(item => item.children && item.children.length > 0).length
})

const fileCount = computed(() => {
  return currentItems.value.filter(item => !item.children || item.children.length === 0).length
})

// 方法定义
const removeFileExtension = (filename) => {
  if (!filename) return ''
  return filename.replace(/\.[^/.]+$/, '')
}

const getItemIcon = (item) => {
  if (item.children && item.children.length > 0) {
    return 'folder-o'
  }

  const fileName = item.fileName?.toLowerCase() || ''
  if (fileName.includes('.pdf')) {
    if (item.filePath?.includes('/manual')) {
      return 'description'
    }
    return 'photo-o'
  } else if (fileName.includes('.mp4')) {
    return 'video-o'
  } else if (item.filePath?.includes('/product')) {
    return 'goods-collect-o'
  }
  return 'notes-o'
}

const getItemIconColor = (item) => {
  if (item.children && item.children.length > 0) {
    return '#1989fa'
  }

  const fileName = item.fileName?.toLowerCase() || ''
  if (fileName.includes('.pdf')) {
    if (item.filePath?.includes('/manual')) {
      return '#ff976a'
    }
    return '#07c160'
  } else if (fileName.includes('.mp4')) {
    return '#ee0a24'
  } else if (item.filePath?.includes('/product')) {
    return '#ffd21e'
  }
  return '#969799'
}

const handleMenuItemClick = (item) => {
  // 清除高亮状态
  clearHighlight()

  if (item.children && item.children.length > 0) {
    // 如果有子项，进入下一级
    currentPath.value.push(item)
  } else {
    // 如果是文件，触发点击事件
    emit('menu-click', item)
  }
}

const goBack = () => {
  if (currentPath.value.length > 0) {
    currentPath.value.pop()
  }
  // 清除高亮状态
  clearHighlight()
}

const goToRoot = () => {
  currentPath.value = []
  // 清除高亮状态
  clearHighlight()
}

const goToPath = (index) => {
  currentPath.value = currentPath.value.slice(0, index + 1)
  // 清除高亮状态
  clearHighlight()
}

// 搜索相关方法
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    handleClearSearch()
    return
  }

  isSearching.value = true
  // 清除高亮状态
  clearHighlight()

  const keyword = searchKeyword.value.toLowerCase()
  searchResults.value = allMenuItems.value.filter(item => {
    const fileName = removeFileExtension(item.fileName).toLowerCase()
    const description = (item.description || '').toLowerCase()
    return fileName.includes(keyword) || description.includes(keyword)
  })
}

const handleClearSearch = () => {
  searchKeyword.value = ''
  isSearching.value = false
  searchResults.value = []
  // 清除高亮状态
  clearHighlight()
}

const highlightSearchText = (text) => {
  if (!searchKeyword.value.trim()) return removeFileExtension(text)

  const keyword = searchKeyword.value.trim()
  const fileName = removeFileExtension(text)
  const regex = new RegExp(`(${keyword})`, 'gi')
  return fileName.replace(regex, '<mark>$1</mark>')
}

const getItemPath = (item) => {
  // 获取菜单项的路径
  const path = findItemPath(props.menuData, item.id)
  return path.map(p => removeFileExtension(p.fileName)).join(' > ')
}

const findItemPath = (items, targetId, currentPath = []) => {
  for (const item of items) {
    const newPath = [...currentPath, item]
    if (item.id === targetId) {
      return newPath
    }
    if (item.children && item.children.length > 0) {
      const found = findItemPath(item.children, targetId, newPath)
      if (found.length > 0) {
        return found
      }
    }
  }
  return []
}

const getFileTypeText = (item) => {
  const fileName = item.fileName?.toLowerCase() || ''
  if (fileName.includes('.pdf')) {
    if (item.filePath?.includes('/manual')) {
      return t('detail.manual')
    }
    return t('detail.diagram')
  } else if (fileName.includes('.mp4')) {
    return t('detail.video')
  } else if (item.filePath?.includes('/product')) {
    return t('detail.product')
  }
  return t('detail.file')
}

// 扁平化菜单数据用于搜索
const flattenMenuItems = (items, result = []) => {
  for (const item of items) {
    if (!item.children || item.children.length === 0) {
      result.push(item)
    }
    if (item.children && item.children.length > 0) {
      flattenMenuItems(item.children, result)
    }
  }
  return result
}

// 根据文件路径查找菜单项
const findMenuItemByPath = (filePath) => {
  if (!filePath || !props.menuData) return null

  // 处理BOM路径转换 - 参考详情页面的逻辑
  let searchPath = filePath
  if (searchPath.includes('/boms/')) {
    searchPath = searchPath.replace('/boms/', '/').replace('.png', '.pdf')
  }

  // 递归搜索菜单数据
  const searchInMenu = (items) => {
    for (const item of items) {
      // 检查当前项的filePath是否匹配
      if (item.filePath === searchPath) {
        return item
      }

      // 如果有子项，递归搜索
      if (item.children && item.children.length > 0) {
        const found = searchInMenu(item.children)
        if (found) return found
      }
    }
    return null
  }

  return searchInMenu(props.menuData)
}

// 获取菜单项的完整路径（从根到目标项）
const getMenuItemFullPath = (targetItem) => {
  if (!targetItem || !props.menuData) return []

  const findPath = (items, target, currentPath = []) => {
    for (const item of items) {
      const newPath = [...currentPath, item]

      if (item.id === target.id) {
        return newPath
      }

      if (item.children && item.children.length > 0) {
        const found = findPath(item.children, target, newPath)
        if (found.length > 0) {
          return found
        }
      }
    }
    return []
  }

  return findPath(props.menuData, targetItem)
}

// 内部方法：自动展开菜单到指定路径
const internalExpandToPath = async (filePath) => {
  if (!filePath) return false

  // URL解码处理
  let decodedPath = filePath
  try {
    if (decodedPath.includes('%')) {
      decodedPath = decodeURIComponent(decodedPath)
    }
  } catch (error) {
    // URL解码失败，使用原始值
  }

  // 查找目标菜单项
  const targetItem = findMenuItemByPath(decodedPath)
  if (!targetItem) {
    return false
  }

  // 获取完整路径
  const fullPath = getMenuItemFullPath(targetItem)
  if (fullPath.length === 0) {
    return false
  }

  // 设置当前路径（排除目标项本身，只展开到父级）
  const parentPath = fullPath.slice(0, -1)
  currentPath.value = parentPath

  // 等待DOM更新后滚动到目标项
  await nextTick()
  scrollToHighlightedItem()

  return true
}

// 滚动到高亮的菜单项
const scrollToHighlightedItem = () => {
  if (!highlightedItemId.value) return

  const menuContainer = document.querySelector('.normal-menu')
  const highlightedElement = document.querySelector('.menu-item--highlighted')

  if (menuContainer && highlightedElement) {
    // 计算滚动位置，使目标项显示在容器中央
    const containerRect = menuContainer.getBoundingClientRect()
    const elementRect = highlightedElement.getBoundingClientRect()

    const scrollTop = menuContainer.scrollTop +
      (elementRect.top - containerRect.top) -
      (containerRect.height / 2) +
      (elementRect.height / 2)

    menuContainer.scrollTo({
      top: Math.max(0, scrollTop),
      behavior: 'smooth'
    })
  }
}

// 清除内部高亮状态（不影响props传入的高亮状态）
const clearHighlight = () => {
  internalHighlightedItemId.value = null
}

// 监听菜单数据变化
watch(() => props.menuData, (newData) => {
  if (newData && newData.length > 0) {
    currentPath.value = []
    allMenuItems.value = flattenMenuItems(newData)
    // 清除内部高亮状态
    clearHighlight()
  }
}, { immediate: true })

// 监听expandToPath props变化，自动展开菜单
watch(() => props.expandToPath, async (newPath, oldPath) => {
  // 只有当路径发生变化且不为空时才执行展开
  if (newPath && newPath !== oldPath && props.menuData.length > 0) {
    try {
      await internalExpandToPath(newPath)
    } catch (error) {
      // 菜单自动展开失败
    }
  }
}, { immediate: true })

// 监听highlightItemId props变化，滚动到高亮项
watch(() => props.highlightItemId, async (newId, oldId) => {
  if (newId && newId !== oldId) {
    // 等待DOM更新后滚动到高亮项
    await nextTick()
    scrollToHighlightedItem()
  }
})

// 监听搜索关键词变化
watch(searchKeyword, (newKeyword) => {
  if (newKeyword.trim()) {
    // 延迟搜索，避免频繁搜索
    setTimeout(() => {
      if (searchKeyword.value === newKeyword) {
        handleSearch()
      }
    }, 300)
  } else {
    handleClearSearch()
  }
})

// 暴露方法（保留向后兼容性，但主要通过props控制）
defineExpose({
  goBack,
  currentPath,
  clearSearch: handleClearSearch,
  search: handleSearch,
  clearHighlight,
  scrollToHighlightedItem,
  // 保留expandToPath方法以兼容现有代码
  expandToPath: internalExpandToPath
})
</script>

<style scoped>
.mobile-menu {
  height: 100%;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.search-section {
  padding: 6px 10px;
  background: white;
  border-bottom: 1px solid #eee;
}

/* 搜索框样式优化 */
.search-section :deep(.van-search) {
  padding: 0;
}

.search-section :deep(.van-search__content) {
  height: 28px;
  padding-left: 6px;
  padding-right: 6px;
  border-radius: 14px;
}

.search-section :deep(.van-field__control) {
  font-size: 13px;
  line-height: 1.3;
}

.search-section :deep(.van-search__action) {
  padding-left: 6px;
  font-size: 13px;
  color: #1989fa;
  font-weight: 500;
}

.search-section :deep(.van-search__action):active {
  opacity: 0.7;
}

/* 搜索框图标和占位符优化 */
.search-section :deep(.van-field__left-icon) {
  font-size: 14px;
  color: #969799;
}

.search-section :deep(.van-field__clear) {
  font-size: 14px;
}

.search-section :deep(.van-field__control::placeholder) {
  font-size: 13px;
  color: #c8c9cc;
}

/* 搜索框聚焦状态优化 */
.search-section :deep(.van-search__content--focus) {
  border-color: #1989fa;
  box-shadow: 0 0 0 1px rgba(25, 137, 250, 0.2);
}

.menu-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.normal-menu {
  flex: 1;
  overflow-y: auto;
}

.mobile-menu :deep(.van-tree-select) {
  height: 100%;
}

.mobile-menu :deep(.van-tree-select__nav) {
  background: white;
  border-right: 1px solid #eee;
}

.mobile-menu :deep(.van-tree-select__content) {
  background: #f8f9fa;
}

/* 搜索相关样式 */
.search-results {
  flex: 1;
  overflow-y: auto;
}

.no-results {
  padding: 32px 16px;
  text-align: center;
}

.search-list {
  padding: 4px 0;
}

.search-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background: white;
  margin-bottom: 1px;
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 44px;
}

.search-item:active {
  background: #e8f4ff;
  transform: scale(0.98);
  transition: all 0.1s ease;
}

.search-item .item-content {
  flex: 1;
  min-width: 0;
}

.search-item .item-path {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-item .item-name :deep(mark) {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;
}

/* 菜单统计和状态 */
.menu-stats {
  padding: 4px 10px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  min-height: 24px;
  display: flex;
  align-items: center;
}

.stats-text {
  font-size: 10px;
  color: #666;
  line-height: 1.2;
  font-weight: 500;
}

.menu-loading {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
}

/* 移除原来的返回按钮样式，已整合到面包屑中 */

.menu-content {
  padding: 4px 0;
  flex: 1;
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background: white;
  margin-bottom: 1px;
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 44px;
}

.menu-item:active {
  background: #e8f4ff;
  transform: scale(0.98);
  transition: all 0.1s ease;
}

/* 高亮菜单项样式 */
.menu-item--highlighted {
  background: #fff7e6 !important;
  border-left: 3px solid #ff8c00;
  position: relative;
}

.menu-item--highlighted::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #ff8c00, #ffa500);
  border-radius: 0 2px 2px 0;
}

.menu-item--highlighted .item-name {
  color: #d2691e;
  font-weight: 600;
}

.menu-item--highlighted .item-icon {
  color: #ff8c00 !important;
}

.item-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  word-break: break-all;
}

.item-desc {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
  line-height: 1.3;
}

.item-count {
  font-size: 11px;
  color: #999;
  margin-top: 2px;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-arrow {
  color: #c8c9cc;
}

.item-type {
  font-size: 11px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 10px;
  white-space: nowrap;
}

/* 面包屑导航样式 */
.breadcrumb {
  padding: 6px 12px;
  background: white;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 36px;
}

.breadcrumb-back {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: #f7f8fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  flex-shrink: 0;
  color: #646566;
}

.breadcrumb-back:active {
  background: #e8f4ff;
  color: #1989fa;
  transform: scale(0.95);
}

.breadcrumb-path {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 2px;
  overflow-x: auto;
  white-space: nowrap;
  min-width: 0;
}

.breadcrumb-path::-webkit-scrollbar {
  display: none;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 12px;
  color: #1989fa;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  transition: background-color 0.2s;
  flex-shrink: 0;
  line-height: 1.3;
}

.breadcrumb-item:active {
  background-color: rgba(25, 137, 250, 0.1);
}

.breadcrumb-item:last-child {
  color: #333;
  cursor: default;
  font-weight: 500;
}

.breadcrumb-item:last-child:active {
  background-color: transparent;
}

.breadcrumb-separator {
  color: #c8c9cc;
  font-size: 10px;
  margin: 0 1px;
  flex-shrink: 0;
}

/* 空状态样式 */
.empty-menu {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: #969799;
}

.empty-menu .van-icon {
  margin-bottom: 8px;
}

/* 响应式设计 - 移动端优先 */

/* 小屏手机适配 (宽度 ≤ 375px) */
@media (max-width: 375px) {
  .search-section {
    padding: 4px 8px;
  }

  .search-section :deep(.van-search__content) {
    height: 26px;
    padding-left: 5px;
    padding-right: 5px;
    border-radius: 13px;
  }

  .search-section :deep(.van-field__control) {
    font-size: 12px;
  }

  .search-section :deep(.van-search__action) {
    padding-left: 5px;
    font-size: 12px;
  }

  .search-section :deep(.van-field__left-icon) {
    font-size: 13px;
  }

  .menu-item,
  .search-item {
    padding: 8px 10px;
    min-height: 40px;
  }

  .breadcrumb {
    padding: 4px 8px;
    min-height: 32px;
  }

  .breadcrumb-back {
    width: 24px;
    height: 24px;
  }

  .breadcrumb-item {
    font-size: 11px;
    padding: 1px 4px;
  }

  .menu-stats {
    padding: 4px 8px;
  }

  .stats-text {
    font-size: 10px;
  }

  .item-name {
    font-size: 13px;
  }

  .item-count {
    font-size: 10px;
  }

  .item-type {
    font-size: 10px;
    padding: 1px 4px;
  }
}

/* 基础横屏模式适配 (所有横屏设备) */
@media (orientation: landscape) {
  .search-section {
    padding: 4px 8px;
  }

  .search-section :deep(.van-search__content) {
    height: 24px;
    border-radius: 12px;
  }

  .search-section :deep(.van-field__control) {
    font-size: 12px;
  }

  .search-section :deep(.van-field__control::placeholder) {
    font-size: 11px; /* 横屏模式下减小占位符文字 */
  }

  .search-section :deep(.van-search__action) {
    font-size: 12px;
  }

  .search-section :deep(.van-field__left-icon) {
    font-size: 12px; /* 横屏模式下减小搜索图标 */
  }

  .menu-item,
  .search-item {
    padding: 6px 10px;
    min-height: 36px;
  }

  .item-name {
    font-size: 13px;
    line-height: 1.3;
  }

  .item-count {
    font-size: 10px;
  }

  .item-type {
    font-size: 10px;
    padding: 1px 5px;
  }

  .breadcrumb {
    padding: 3px 8px;
    min-height: 28px;
    gap: 6px;
  }

  .breadcrumb-back {
    width: 20px; /* 横屏模式下减小返回按钮 */
    height: 20px;
  }

  .breadcrumb-item {
    font-size: 11px;
    padding: 1px 3px;
    gap: 2px;
  }

  .menu-stats {
    padding: 3px 8px;
    min-height: 20px;
  }

  .stats-text {
    font-size: 9px;
  }
}

/* 大屏手机横屏模式 (宽度 > 567px) */
@media (orientation: landscape) and (min-width: 567px) {
  .search-section {
    padding: 3px 10px;
  }

  .search-section :deep(.van-search__content) {
    height: 22px;
    padding-left: 4px;
    padding-right: 4px;
    border-radius: 11px;
  }

  .search-section :deep(.van-field__control) {
    font-size: 11px;
  }

  .search-section :deep(.van-field__control::placeholder) {
    font-size: 10px; /* 大屏横屏下进一步减小占位符文字 */
  }

  .search-section :deep(.van-search__action) {
    padding-left: 4px;
    font-size: 11px;
  }

  .search-section :deep(.van-field__left-icon) {
    font-size: 11px; /* 大屏横屏下减小搜索图标 */
  }

  .menu-item,
  .search-item {
    padding: 5px 10px;
    min-height: 32px;
  }

  .item-name {
    font-size: 12px;
  }

  .item-count {
    font-size: 9px;
  }

  .item-type {
    font-size: 9px;
    padding: 1px 4px;
  }

  .breadcrumb {
    padding: 2px 10px;
    min-height: 24px;
    gap: 4px;
  }

  .breadcrumb-back {
    width: 18px; /* 大屏横屏下进一步减小返回按钮 */
    height: 18px;
  }

  .breadcrumb-item {
    font-size: 10px;
    padding: 1px 2px;
  }

  .menu-stats {
    padding: 2px 10px;
    min-height: 18px;
  }

  .stats-text {
    font-size: 8px;
  }
}

/* 平板设备适配 (宽度 768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .search-section {
    padding: 6px 12px;
  }

  .search-section :deep(.van-search__content) {
    height: 32px;
    padding-left: 8px;
    padding-right: 8px;
    border-radius: 16px;
  }

  .search-section :deep(.van-field__control) {
    font-size: 14px;
  }

  .search-section :deep(.van-field__control::placeholder) {
    font-size: 13px; /* 平板设备下占位符文字大小 */
  }

  .search-section :deep(.van-search__action) {
    padding-left: 8px;
    font-size: 14px;
  }

  .search-section :deep(.van-field__left-icon) {
    font-size: 15px; /* 平板设备下搜索图标大小 */
  }

  .menu-item,
  .search-item {
    padding: 10px 16px; /* 减少垂直内边距 */
    min-height: 44px; /* 减小最小高度 */
  }

  .item-name {
    font-size: 14px; /* 减小字体大小 */
    line-height: 1.3;
  }

  .item-count {
    font-size: 11px;
  }

  .item-type {
    font-size: 11px;
    padding: 2px 8px;
  }

  .breadcrumb {
    padding: 6px 16px; /* 减少垂直内边距 */
    min-height: 36px; /* 减小最小高度 */
    gap: 8px;
  }

  .breadcrumb-back {
    width: 28px; /* 平板设备下减小返回按钮 */
    height: 28px;
  }

  .breadcrumb-item {
    font-size: 12px; /* 减小字体大小 */
    padding: 2px 6px;
    gap: 3px;
  }

  .menu-stats {
    padding: 4px 16px; /* 减少垂直内边距 */
    min-height: 24px; /* 减小最小高度 */
  }

  .stats-text {
    font-size: 10px; /* 减小字体大小 */
  }

  .item-icon {
    width: 24px; /* 减小图标尺寸 */
    height: 24px;
    margin-right: 14px;
  }
}

/* 触摸优化和交互反馈 */
.breadcrumb-item,
.menu-item,
.search-item {
  -webkit-tap-highlight-color: transparent;
  transition: all 0.2s ease;
}

.menu-item:active,
.search-item:active {
  transform: scale(0.98);
}

.breadcrumb-back:active {
  transform: scale(0.95);
}

.van-button {
  -webkit-tap-highlight-color: transparent;
}

/* 确保所有交互元素满足最小触摸目标 */
.breadcrumb-back,
.breadcrumb-item {
  min-width: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-icon {
  min-width: 24px;
  min-height: 24px;
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .search-section {
    border-bottom: 0.5px solid #eee;
  }

  .breadcrumb {
    border-bottom: 0.5px solid #eee;
  }

  .menu-stats {
    border-bottom: 0.5px solid #eee;
  }

  .product-params-section {
    border-top: 0.5px solid #f0f0f0;
    border-bottom: 0.5px solid #f0f0f0;
  }
}
</style>
