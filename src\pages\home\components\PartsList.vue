<template>
  <div class="parts-list">
    <!-- 加载状态 -->
    <van-loading v-if="loading && data.length === 0" class="loading-center">
      {{ $t("home.loading") }}
    </van-loading>

    <!-- 配件列表 -->
    <div v-else class="parts-items">
      <div
        v-for="(group, groupIndex) in data"
        :key="group.id || groupIndex"
        class="parts-group-card"
      >
        <!-- 设备信息头部 - 紧凑设计 -->
        <div class="equipment-header">
          <div class="equipment-thumbnail">
            <van-image
              :src="getEquipmentImage(group.productFileList)"
              fit="cover"
              @click="handleEquipmentImageClick(group)"
              class="equipment-img"
            >
              <template #error>
                <div class="image-placeholder">
                  <van-icon name="photo-fail" size="16" />
                </div>
              </template>
            </van-image>
          </div>

          <div class="equipment-meta">
            <div class="equipment-title">
              <span class="model-text">{{ group.model }}</span>
              <span class="service-code-text">{{ group.serviceCode }}</span>
            </div>
            <div class="equipment-spec" v-if="group.materielSpecification">
              {{ group.materielSpecification }}
            </div>
          </div>
        </div>

        <!-- 配件列表 - 全新卡片式设计 -->
        <div class="parts-list-container">
          <div
            v-for="(part, partIndex) in getDisplayParts(group.spartList || [])"
            :key="part.id || partIndex"
            class="part-card"
          >
            <!-- 配件卡片头部 -->
            <div class="part-card-header">
              <!-- 配件主要信息 -->
              <div class="part-main-info">
                <h3 class="part-title" @click="$emit('viewDetails', part)">
                  {{ part.materielNameEn || part.materielName }}
                </h3>
                <div class="part-meta">
                  <span class="part-number"
                    >{{ $t("home.partNumber") }}: {{ part.materielCode }}</span
                  >
                </div>
              </div>

              <!-- 操作按钮工具栏 -->
              <div class="part-actions-toolbar">
                <van-button
                  plain
                  size="mini"
                  :loading="part.favoritLoad"
                  @click="$emit('addToFavorites', part)"
                  class="action-btn favorite-btn"
                >
                  <van-icon
                    :name="part.existsFavorites === '1' ? 'star' : 'star-o'"
                    :color="
                      part.existsFavorites === '1' ? '#f39c12' : '#969799'
                    "
                    size="18"
                  />
                </van-button>

                <van-button
                  plain
                  size="mini"
                  @click="handleViewBom(part)"
                  class="action-btn bom-btn"
                  :title="$t('home.viewBomWithLocation')"
                >
                  <van-icon name="eye-o" size="16" />
                </van-button>
              </div>
            </div>

            <!-- 配件详细信息 -->
            <div class="part-details">
              <!-- 描述信息 -->
              <div class="part-description" v-if="part.materielSpecification">
                <span class="detail-label">{{ $t("home.description") }}:</span>
                <span class="detail-value">{{
                  part.materielSpecification
                }}</span>
              </div>

              <!-- 组件信息 -->
              <div class="part-component" v-if="part.filePath">
                <span class="detail-label">{{ $t("home.component") }}:</span>
                <span class="detail-value">{{
                  getComponentName(part.filePath)
                }}</span>
              </div>

              <!-- 库存信息（仅管理员可见） -->
              <div
                v-if="userInfo.userType == 1 && part.warehouseAmount"
                class="part-stock"
              >
                <span class="stock-label">{{ $t("home.stock") }}:</span>
                <span class="stock-value">{{
                  formatStock(part.warehouseAmount)
                }}</span>
                <span v-if="part.shelfCode" class="shelf-code"
                  >({{ part.shelfCode }})</span
                >
              </div>
            </div>

            <!-- 价格和操作区域 -->
            <div class="part-footer">
              <div class="price-quantity-section">
                <!-- 数量控制 -->
                <div class="quantity-control">
                  <span class="quantity-label">{{ $t("home.quantity") }}</span>
                  <van-stepper
                    v-model="part.amount"
                    :min="1"
                    @change="onAmountChange(part)"
                    button-size="28"
                    input-width="50"
                    class="stepper"
                  />
                </div>

                <!-- 价格显示 -->
                <div class="price-display">
                  <div class="price-amount">
                    <span class="currency">{{ getCurrencySymbol() }}</span>
                    <span class="price-value">{{
                      formatPrice(part.totalPrice || part.price)
                    }}</span>
                  </div>
                </div>
              </div>

              <!-- 购物车按钮 -->
              <div class="cart-action">
                <van-button
                  :type="part.existsCart === '1' ? 'warning' : 'primary'"
                  size="normal"
                  :loading="part.buyLoad"
                  @click="$emit('addToCart', part)"
                  class="add-cart-btn"
                  round
                >
                  <van-icon
                    :name="
                      part.existsCart === '1'
                        ? 'shopping-cart'
                        : 'shopping-cart-o'
                    "
                    size="16"
                  />
                </van-button>
              </div>
            </div>
          </div>

          <!-- 查看所有按钮 -->
          <div
            v-if="shouldShowViewAllButton(group.spartList || [])"
            class="view-all-container"
          >
            <van-button
              plain
              size="small"
              @click="toggleShowAll(groupIndex)"
              class="view-all-btn"
            >
              {{ group.showAll ? $t('home.collapse') : $t('home.viewAll') }}
              <van-icon
                :name="group.showAll ? 'arrow-up' : 'arrow-down'"
                size="14"
              />
            </van-button>
          </div>
        </div>
      </div>

      <!-- 无数据提示 -->
      <van-empty
        v-if="!loading && data.length === 0"
        :description="$t('home.noPartsData')"
        class="empty-state"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import apiService from "@/utils/api";
import { useUserStore } from "@/stores/user";

// 用户信息
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo || {});

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  hasMore: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "addToCart",
  "addToFavorites",
  "viewBom",
  "viewDetails",
  "navigateToDetail",
]);

// 每个组默认显示的配件数量
const DEFAULT_DISPLAY_COUNT = 3;

// 获取要显示的配件列表
const getDisplayParts = (partsList) => {
  if (!partsList || partsList.length === 0) return [];

  // 查找当前组的showAll状态
  const currentGroup = props.data.find(group => group.spartList === partsList);
  if (currentGroup && currentGroup.showAll) {
    return partsList;
  }

  return partsList.slice(0, DEFAULT_DISPLAY_COUNT);
};

// 判断是否应该显示"查看所有"按钮
const shouldShowViewAllButton = (partsList) => {
  return partsList && partsList.length > DEFAULT_DISPLAY_COUNT;
};

// 切换显示所有/收起
const toggleShowAll = (groupIndex) => {
  if (props.data[groupIndex]) {
    // 直接修改数据对象的showAll属性
    props.data[groupIndex].showAll = !props.data[groupIndex].showAll;
  }
};

// 获取设备主图
const getEquipmentImage = (fileList) => {
  if (!fileList || fileList.length === 0) return "";

  const productImages = fileList.filter((path) => {
    const fileName = path.split("/").pop();
    return (
      fileName.includes("product") &&
      (fileName.includes(".jpg") || fileName.includes(".png"))
    );
  });

  return productImages.length > 0 ? `/fileviewer${productImages[0]}` : "";
};

// 获取组件名称
const getComponentName = (filePath) => {
  if (!filePath) return "";
  return filePath.split("/").pop().replace(".pdf", "");
};



// 处理设备图片点击 - 跳转到产品详情页
const handleEquipmentImageClick = (group) => {
  // 构建产品详情页面的路由参数
  const productParams = constructProductParams(group);

  // 触发导航事件
  emit('navigateToDetail', productParams);
};

// 构建产品详情页参数
const constructProductParams = (group) => {
  // 构建以 /product 结尾的路径
  let productPath = '';

  // 如果有产品文件路径，使用第一个产品文件的路径
  if (group.productFileList && group.productFileList.length > 0) {
    const productFile = group.productFileList.find(path =>
      path.includes('/product/') || path.includes('product')
    );

    if (productFile) {
      // 移除文件名和product目录，然后添加 /product 后缀
      const pathParts = productFile.split('/');
      pathParts.pop(); // 移除文件名

      // 如果最后一个部分是 'product'，也移除它
      if (pathParts[pathParts.length - 1] === 'product') {
        pathParts.pop();
      }

      productPath = pathParts.join('/') + '/product';
    }
  }

  // 如果没有找到合适的路径，构建一个默认路径
  if (!productPath) {
    productPath = `/Equipment/${group.model || 'UNKNOWN'}/product`;
  }

  return {
    path: '/detail',
    query: {
      params: encodeURIComponent(productPath), // URL编码params值
      equipmentId: group.id,
      model: group.model,
      serviceCode: group.serviceCode,
      materielCode: group.materielCode || '',
      materielName: group.materielName || '',
      materielNameEn: group.materielNameEn || group.model || '',
      price: group.price || group.totalPrice,
      amount: group.amount,
      existsCart: group.existsCart,
      existsFavorites: group.existsFavorites
    }
  };
};

// 数量变化处理
const onAmountChange = (item) => {
  item.totalPrice = parseFloat(
    (parseFloat(item.price || 0) * parseFloat(item.amount || 1)).toFixed(2)
  );
};

// 格式化价格
const formatPrice = (value) => {
  return apiService.utils.formatPrice(value);
};

// 格式化库存
const formatStock = (value) => {
  return apiService.utils.formatStock(value);
};

// 获取货币符号
const getCurrencySymbol = () => {
  const currency = userInfo.value.currency;
  switch (currency) {
    case "1":
      return "¥";
    case "2":
      return "$";
    case "3":
      return "€";
    default:
      return "";
  }
};

// 处理查看BOM事件，传递配件信息用于定位
const handleViewBom = (part) => {
  // 构建包含配件定位信息的对象
  const bomData = {
    // BOM基础信息
    filePath: part.filePath,
    bomId: part.bomId,
    materielCode: part.materielCode,
    materielNameEn: part.materielNameEn,
    materielName: part.materielName,

    // 配件定位信息
    indexNo: part.indexNo,
    id: part.id,
    bomItemsId: part.bomItemsId,
    existsCart: part.existsCart,
    existsFavorites: part.existsFavorites,
    quantity: part.quantity,
    amount: part.amount,
    price: part.price
  };

  emit('viewBom', bomData);
};
</script>

<style lang="scss" scoped>
.parts-list {
  padding: 0;
  background: #f5f7fa;
}

.loading-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  gap: 12px;

  :deep(.van-loading__text) {
    color: #646566;
    font-size: 14px;
    margin-top: 8px;
  }

  :deep(.van-loading__spinner) {
    color: #e74c3c;
  }
}

.parts-items {
  padding: 8px;
}

// 设备组卡片样式
.parts-group-card {
  background: white;
  margin-bottom: 12px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;

  &:last-child {
    margin-bottom: 0;
  }
}

// 设备头部样式 - 紧凑设计
.equipment-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e9ecef;

  .equipment-thumbnail {
    width: 50px;
    height: 50px;
    margin-right: 12px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .equipment-img {
      width: 100%;
      height: 100%;
    }

    .image-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      background: #f5f5f5;
      color: #969799;
    }
  }

  .equipment-meta {
    flex: 1;
    min-width: 0;

    .equipment-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;

      .model-text {
        font-size: 14px;
        font-weight: 600;
        color: #e74c3c;
        text-decoration: underline;
      }

      .service-code-text {
        font-size: 12px;
        color: #646566;
        background: #f2f3f5;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }

    .equipment-spec {
      font-size: 12px;
      color: #969799;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

// 配件列表容器
.parts-list-container {
  padding: 0;
}

// 配件卡片样式
.part-card {
  margin: 0 16px 12px 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  // 添加触摸反馈
  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
  }

  &:last-child {
    margin-bottom: 0;
  }

  // 添加微妙的渐变背景
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(231, 76, 60, 0.1),
      transparent
    );
  }
}

// 配件卡片头部
.part-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f5f5f5;

  .part-main-info {
    flex: 1;
    min-width: 0;
    margin-right: 12px;

    .part-title {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: #e74c3c;
      line-height: 1.4;
      cursor: pointer;
      text-decoration: underline;
      word-break: break-word;

      &:hover {
        color: #c0392b;
      }
    }

    .part-meta {
      .part-number {
        font-size: 13px;
        color: #646566;
        background: #f7f8fa;
        padding: 4px 8px;
        border-radius: 6px;
        display: inline-block;
      }
    }
  }

  // 操作按钮工具栏
  .part-actions-toolbar {
    display: flex;
    gap: 6px;
    flex-shrink: 0;

    .action-btn {
      min-width: 36px;
      height: 36px;
      padding: 0;
      border-radius: 8px;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;

      // 添加触摸反馈
      &:active {
        transform: scale(0.95);
      }

      // 添加波纹效果
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
      }

      &:active::after {
        width: 40px;
        height: 40px;
      }

      &.favorite-btn {
        border: 1px solid #f39c12;

        &:hover {
          background: #fef9e7;
          border-color: #e67e22;
        }

        &:active {
          background: #fcf3cf;
        }
      }

      &.bom-btn {
        border: 1px solid #e74c3c;
        color: #e74c3c;

        &:hover {
          background: #fdf2f2;
          border-color: #c0392b;
        }

        &:active {
          background: #fadbd8;
        }
      }

      &.detail-btn {
        border: 1px solid #969799;
        color: #969799;

        &:hover {
          background: #f5f5f5;
          border-color: #646566;
        }

        &:active {
          background: #ebedf0;
        }
      }
    }
  }
}

// 配件详细信息
.part-details {
  padding: 6px 16px 6px 16px;

  .part-description,
  .part-component {
    margin-bottom: 0px;
    font-size: 13px;
    line-height: 1.4;

    .detail-label {
      color: #969799;
      font-weight: 500;
      margin-right: 4px;
    }

    .detail-value {
      color: #323233;
      word-break: break-word;
    }
  }

  .part-stock {
    font-size: 12px;
    color: #e74c3c;
    background: #fdf2f2;
    padding: 6px 10px;
    border-radius: 6px;
    display: inline-block;

    .stock-label {
      font-weight: 500;
    }

    .stock-value {
      font-weight: 600;
      margin: 0 4px;
    }

    .shelf-code {
      color: #c0392b;
    }
  }
}

// 配件底部价格和操作区域
.part-footer {
  padding: 16px;
  background: #fafbfc;
  border-top: 1px solid #f0f0f0;

  .price-quantity-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .quantity-control {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .quantity-label {
        font-size: 12px;
        color: #646566;
        margin-bottom: 6px;
        font-weight: 500;
      }

      .stepper {
        :deep(.van-stepper__input) {
          width: 50px;
          font-size: 14px;
          font-weight: 600;
        }

        :deep(.van-stepper__minus),
        :deep(.van-stepper__plus) {
          width: 28px;
          height: 28px;
          border-radius: 6px;
        }
      }
    }

    .price-display {
      text-align: right;

      .price-amount {
        .currency {
          font-size: 20px;
          color: #e74c3c;
          margin-right: 3px;
          font-weight: 600;
        }

        .price-value {
          font-size: 20px;
          font-weight: 700;
          color: #e74c3c;
        }
      }
    }
  }

  .cart-action {
    display: flex;
    justify-content: center;

    .add-cart-btn {
      width: 100%;
      height: 44px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 22px;
      box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);

      &:active {
        transform: scale(0.98);
      }

      // 已在购物车状态
      &.van-button--warning {
        background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
      }

      // 未在购物车状态
      &.van-button--primary {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
      }
    }
  }
}

// 空状态样式
.empty-state {
  padding: 80px 20px;
  text-align: center;

  :deep(.van-empty__description) {
    color: #969799;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .parts-items {
    padding: 6px;
  }

  .parts-group-card {
    margin-bottom: 10px;
    border-radius: 10px;
  }

  .equipment-header {
    padding: 10px 12px;

    .equipment-thumbnail {
      width: 45px;
      height: 45px;
      margin-right: 10px;
    }

    .equipment-meta {
      .equipment-title {
        .model-text {
          font-size: 13px;
        }

        .service-code-text {
          font-size: 11px;
        }
      }

      .equipment-spec {
        font-size: 11px;
      }
    }
  }

  .part-card {
    margin: 0 12px 10px 12px;
    border-radius: 10px;
  }

  .part-card-header {
    padding: 12px;

    .part-main-info {
      margin-right: 8px;

      .part-title {
        font-size: 15px;
      }

      .part-meta {
        .part-number {
          font-size: 12px;
        }
      }
    }

    .part-actions-toolbar {
      gap: 4px;

      .action-btn {
        min-width: 32px;
        height: 32px;
      }
    }
  }

  .part-details {
    padding: 6px 16px 6px 16px;

    .part-description,
    .part-component {
      font-size: 12px;
    }

    .part-stock {
      font-size: 11px;
    }
  }

  .part-footer {
    padding: 12px;

    .price-quantity-section {
      margin-bottom: 10px;

      .quantity-control {
        .quantity-label {
          font-size: 11px;
        }
      }

      .price-display {
        .price-amount {
          .currency {
            font-size: 18px;
          }

          .price-value {
            font-size: 18px;
          }
        }
      }
    }

    .cart-action {
      .add-cart-btn {
        height: 40px;
        font-size: 15px;
        border-radius: 20px;
      }
    }
  }

  // 查看所有按钮容器
  .view-all-container {
    padding: 12px;
    text-align: center;
    border-top: 1px solid #f0f0f0;

    .view-all-btn {
      color: #1989fa;
      border-color: #1989fa;
      font-size: 13px;
      padding: 8px 16px;

      &:active {
        background-color: #f0f8ff;
      }
    }
  }
}

// 超小屏幕适配
@media (max-width: 375px) {
  .parts-items {
    padding: 4px;
  }

  .part-card {
    margin: 0 8px 8px 8px;
  }

  .part-card-header {
    padding: 10px;

    .part-actions-toolbar {
      .action-btn {
        min-width: 28px;
        height: 28px;
      }
    }
  }

  .part-footer {
    padding: 10px;

    .price-quantity-section {
      .price-display {
        .price-amount {
          .price-value {
            font-size: 16px;
          }
        }
      }
    }

    .cart-action {
      .add-cart-btn {
        height: 36px;
        font-size: 14px;
      }
    }
  }
}
</style>
